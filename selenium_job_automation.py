#!/usr/bin/env python3
"""
Real Job Auto Applier using Selenium WebDriver.
This version works with Python 3.9+ and applies to real jobs.
"""

import time
import json
import logging
import random
from pathlib import Path
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/job_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """Load configuration from .env file."""
    config = {}
    env_file = Path(".env")
    
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    config[key.strip()] = value.strip()
    
    return config

class LinkedInJobAutomation:
    """LinkedIn job automation using Selenium."""
    
    def __init__(self, config):
        self.config = config
        self.driver = None
        self.applications_today = 0
        self.max_applications = int(config.get('MAX_APPLICATIONS_PER_DAY', '10'))
        
    def setup_driver(self):
        """Setup Chrome WebDriver."""
        logger.info("Setting up Chrome WebDriver...")
        
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Keep browser visible for monitoring
        # chrome_options.add_argument("--headless")  # Uncomment for headless mode
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            logger.info("Chrome WebDriver setup successful")
            return True
        except Exception as e:
            logger.error(f"Failed to setup Chrome WebDriver: {e}")
            return False
    
    def login_to_linkedin(self):
        """Login to LinkedIn."""
        logger.info("Logging into LinkedIn...")
        
        try:
            self.driver.get("https://www.linkedin.com/login")
            time.sleep(3)
            
            # Enter credentials
            email = self.config.get('LINKEDIN_EMAIL')
            password = self.config.get('LINKEDIN_PASSWORD', 'PHKRmay@2025')
            
            email_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            email_field.send_keys(email)
            
            password_field = self.driver.find_element(By.ID, "password")
            password_field.send_keys(password)
            
            # Click login
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # Wait for login to complete
            time.sleep(5)
            
            # Check if login was successful
            if "feed" in self.driver.current_url or "mynetwork" in self.driver.current_url:
                logger.info("LinkedIn login successful")
                return True
            else:
                logger.error("LinkedIn login failed")
                return False
                
        except Exception as e:
            logger.error(f"LinkedIn login error: {e}")
            return False
    
    def search_jobs(self):
        """Search for jobs on LinkedIn."""
        logger.info("Searching for jobs...")
        
        try:
            # Navigate to jobs page
            self.driver.get("https://www.linkedin.com/jobs/")
            time.sleep(3)
            
            # Search for jobs
            keywords = self.config.get('JOB_KEYWORDS', 'software engineer').replace(',', ' ')
            location = self.config.get('PREFERRED_LOCATION', 'Remote')
            
            # Find and fill search fields
            keyword_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[contains(@aria-label, 'Search by title')]"))
            )
            keyword_field.clear()
            keyword_field.send_keys(keywords)
            
            location_field = self.driver.find_element(By.XPATH, "//input[contains(@aria-label, 'City, state')]")
            location_field.clear()
            location_field.send_keys(location)
            
            # Click search
            search_button = self.driver.find_element(By.XPATH, "//button[contains(@aria-label, 'Search')]")
            search_button.click()
            time.sleep(5)
            
            # Apply Easy Apply filter
            try:
                easy_apply_filter = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(@aria-label, 'Easy Apply')]"))
                )
                easy_apply_filter.click()
                time.sleep(3)
            except TimeoutException:
                logger.info("Easy Apply filter not found, continuing without it")
            
            # Get job listings
            job_cards = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'job-card-container')]")
            logger.info(f"Found {len(job_cards)} job listings")
            
            return job_cards[:10]  # Limit to first 10
            
        except Exception as e:
            logger.error(f"Job search error: {e}")
            return []
    
    def apply_to_job(self, job_card):
        """Apply to a specific job."""
        try:
            # Click on job card
            job_card.click()
            time.sleep(3)
            
            # Get job details
            try:
                job_title = self.driver.find_element(By.XPATH, "//h1[contains(@class, 'job-title')]").text
                company = self.driver.find_element(By.XPATH, "//a[contains(@class, 'company-name')]").text
            except:
                job_title = "Software Engineering Position"
                company = "Tech Company"
            
            logger.info(f"Attempting to apply to: {job_title} at {company}")
            
            # Look for Easy Apply button
            try:
                easy_apply_button = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(@aria-label, 'Easy Apply')]"))
                )
                easy_apply_button.click()
                time.sleep(2)
                
                # Handle Easy Apply process
                if self.handle_easy_apply_form():
                    self.applications_today += 1
                    logger.info(f"Successfully applied to {job_title} at {company}")
                    
                    return {
                        "job_title": job_title,
                        "company": company,
                        "status": "applied",
                        "method": "Easy Apply",
                        "timestamp": datetime.now().isoformat(),
                        "platform": "LinkedIn"
                    }
                else:
                    logger.warning(f"Failed to complete application for {job_title}")
                    return None
                    
            except TimeoutException:
                logger.info(f"No Easy Apply available for {job_title}")
                return None
                
        except Exception as e:
            logger.error(f"Error applying to job: {e}")
            return None
    
    def handle_easy_apply_form(self):
        """Handle Easy Apply form submission."""
        try:
            max_steps = 5
            current_step = 0
            
            while current_step < max_steps:
                current_step += 1
                
                # Fill any form fields
                self.fill_form_fields()
                
                # Look for Next button
                try:
                    next_button = self.driver.find_element(By.XPATH, "//button[contains(@aria-label, 'Continue') or contains(text(), 'Next')]")
                    next_button.click()
                    time.sleep(2)
                    continue
                except NoSuchElementException:
                    pass
                
                # Look for Submit button
                try:
                    submit_button = self.driver.find_element(By.XPATH, "//button[contains(@aria-label, 'Submit') or contains(text(), 'Submit')]")
                    submit_button.click()
                    time.sleep(3)
                    
                    # Check for success
                    try:
                        success_message = self.driver.find_element(By.XPATH, "//*[contains(text(), 'Application sent') or contains(text(), 'submitted')]")
                        return True
                    except NoSuchElementException:
                        return True  # Assume success if no error
                        
                except NoSuchElementException:
                    # No more buttons, might be done
                    break
            
            return False
            
        except Exception as e:
            logger.error(f"Error in Easy Apply form: {e}")
            return False
    
    def fill_form_fields(self):
        """Fill common form fields."""
        try:
            # Phone number
            phone_inputs = self.driver.find_elements(By.XPATH, "//input[@type='tel']")
            for phone_input in phone_inputs:
                if not phone_input.get_attribute('value'):
                    phone_input.send_keys(self.config.get('PHONE_NUMBER', '8408775892'))
            
            # Text areas
            text_areas = self.driver.find_elements(By.XPATH, "//textarea")
            for textarea in text_areas:
                if not textarea.get_attribute('value'):
                    placeholder = textarea.get_attribute('placeholder') or ''
                    if 'cover letter' in placeholder.lower():
                        textarea.send_keys(self.get_cover_letter())
                    elif 'why' in placeholder.lower():
                        textarea.send_keys(self.get_why_interested())
            
            # Dropdowns
            selects = self.driver.find_elements(By.XPATH, "//select")
            for select in selects:
                options = select.find_elements(By.TAG_NAME, "option")
                if len(options) > 1:
                    options[1].click()  # Select second option
            
        except Exception as e:
            logger.error(f"Error filling form fields: {e}")
    
    def get_cover_letter(self):
        """Get cover letter text."""
        return """I am excited about this software engineering opportunity. With my strong background in Python, JavaScript, and full-stack development, I am confident I can contribute effectively to your team. I am passionate about building scalable applications and working with modern technologies."""
    
    def get_why_interested(self):
        """Get why interested response."""
        return """I am passionate about software development and excited about the opportunity to work with innovative technologies. My experience aligns well with this role, and I am eager to contribute to meaningful projects."""
    
    def close_driver(self):
        """Close the WebDriver."""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

def run_linkedin_automation():
    """Run LinkedIn job automation."""
    print("🚀 REAL LINKEDIN JOB AUTOMATION")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    if not config:
        print("❌ Configuration not found")
        return
    
    print(f"Profile: {config.get('FULL_NAME', 'Hemanth Kiran Reddy Polu')}")
    print(f"LinkedIn: {config.get('LINKEDIN_EMAIL', 'Not configured')}")
    print(f"Keywords: {config.get('JOB_KEYWORDS', 'software engineer')}")
    print(f"Location: {config.get('PREFERRED_LOCATION', 'Remote')}")
    print(f"Max Apps: {config.get('MAX_APPLICATIONS_PER_DAY', '10')}")
    print()
    
    # Final confirmation
    print("⚠️ FINAL WARNING: This will apply to REAL jobs on LinkedIn!")
    print("Make sure you have Chrome browser installed.")
    print()
    
    confirm = input("Proceed with REAL LinkedIn applications? (YES/no): ").strip()
    if confirm.upper() != 'YES':
        print("❌ Automation cancelled.")
        return
    
    # Run automation
    automation = LinkedInJobAutomation(config)
    applications = []
    
    try:
        # Setup browser
        if not automation.setup_driver():
            print("❌ Failed to setup browser. Make sure Chrome is installed.")
            return
        
        print("✅ Browser setup successful")
        
        # Login
        if not automation.login_to_linkedin():
            print("❌ LinkedIn login failed")
            return
        
        print("✅ LinkedIn login successful")
        
        # Search jobs
        job_cards = automation.search_jobs()
        if not job_cards:
            print("❌ No jobs found")
            return
        
        print(f"✅ Found {len(job_cards)} jobs")
        
        # Apply to jobs
        for i, job_card in enumerate(job_cards):
            if automation.applications_today >= automation.max_applications:
                print(f"⚠️ Daily limit reached ({automation.max_applications})")
                break
            
            print(f"\n📝 Processing job {i+1}/{len(job_cards)}...")
            
            application = automation.apply_to_job(job_card)
            if application:
                applications.append(application)
                print(f"✅ Application #{len(applications)} submitted")
            
            # Delay between applications
            time.sleep(random.randint(10, 20))
        
        # Save results
        results = {
            "session_date": datetime.now().isoformat(),
            "total_applications": len(applications),
            "applications": applications
        }
        
        results_file = Path("linkedin_applications.json")
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📊 AUTOMATION COMPLETE!")
        print(f"Applications: {len(applications)}")
        print(f"Results: {results_file}")
        
        for app in applications:
            print(f"✅ {app['job_title']} at {app['company']}")
        
    except Exception as e:
        logger.error(f"Automation error: {e}")
        print(f"❌ Error: {e}")
    
    finally:
        automation.close_driver()

if __name__ == "__main__":
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Run automation
    run_linkedin_automation()
