"""Basic functionality tests for Job Auto Applier."""

import pytest
import asyncio
from pathlib import Path
from datetime import datetime

from job_auto_applier.models.user_profile import (
    UserProfile, PersonalInfo, JobPreferences, ApplicationAnswers,
    ExperienceLevel, WorkType
)
from job_auto_applier.models.job_posting import (
    JobPosting, JobSource, JobType, CompanyInfo, JobRequirements
)
from job_auto_applier.models.application import Application, ApplicationMethod, ApplicationStatus
from job_auto_applier.config.settings import Settings
from job_auto_applier.config.encryption import EncryptionManager


class TestUserProfile:
    """Test user profile functionality."""
    
    def test_create_user_profile(self):
        """Test creating a user profile."""
        personal_info = PersonalInfo(
            first_name="<PERSON>",
            last_name="Do<PERSON>",
            email="<EMAIL>",
            location="San Francisco, CA"
        )
        
        job_preferences = JobPreferences(
            target_roles=["Software Engineer"],
            experience_levels=[ExperienceLevel.ENTRY_LEVEL],
            preferred_locations=["Remote", "San Francisco, CA"],
            work_types=[WorkType.REMOTE]
        )
        
        application_answers = ApplicationAnswers(
            why_interested="I'm passionate about technology and software development.",
            why_good_fit="My skills in Python and web development make me a great fit.",
            career_goals="I want to grow as a software engineer and build impactful products.",
            relevant_experience="I have experience building web applications with Python and React.",
            biggest_achievement="Built a full-stack web application that serves 1000+ users.",
            technical_skills="Python, JavaScript, React, Django, PostgreSQL, AWS"
        )
        
        profile = UserProfile(
            user_id="test_user",
            personal_info=personal_info,
            job_preferences=job_preferences,
            application_answers=application_answers
        )
        
        assert profile.user_id == "test_user"
        assert profile.personal_info.full_name == "John Doe"
        assert len(profile.job_preferences.target_roles) == 1
        assert profile.application_answers.why_interested is not None
    
    def test_profile_serialization(self):
        """Test profile serialization to/from dict."""
        personal_info = PersonalInfo(
            first_name="Jane",
            last_name="Smith",
            email="<EMAIL>",
            location="New York, NY"
        )
        
        job_preferences = JobPreferences(
            target_roles=["Cloud Engineer"],
            experience_levels=[ExperienceLevel.ASSOCIATE],
            preferred_locations=["Remote"],
            work_types=[WorkType.REMOTE]
        )
        
        application_answers = ApplicationAnswers(
            why_interested="Cloud technology fascinates me.",
            why_good_fit="I have AWS certifications and cloud experience.",
            career_goals="Become a cloud architecture expert.",
            relevant_experience="Managed AWS infrastructure for multiple projects.",
            biggest_achievement="Reduced cloud costs by 40% through optimization.",
            technical_skills="AWS, Docker, Kubernetes, Terraform, Python"
        )
        
        profile = UserProfile(
            user_id="test_user_2",
            personal_info=personal_info,
            job_preferences=job_preferences,
            application_answers=application_answers
        )
        
        # Test serialization
        profile_dict = profile.to_dict()
        assert isinstance(profile_dict, dict)
        assert profile_dict["user_id"] == "test_user_2"
        
        # Test deserialization
        restored_profile = UserProfile.from_dict(profile_dict)
        assert restored_profile.user_id == profile.user_id
        assert restored_profile.personal_info.email == profile.personal_info.email


class TestJobPosting:
    """Test job posting functionality."""
    
    def test_create_job_posting(self):
        """Test creating a job posting."""
        company = CompanyInfo(
            name="Tech Corp",
            industry="Technology",
            size="51-200 employees",
            location="San Francisco, CA"
        )
        
        requirements = JobRequirements(
            required_skills=["Python", "Django", "PostgreSQL"],
            required_experience_years=2
        )
        
        job = JobPosting(
            job_id="test_job_1",
            url="https://example.com/jobs/1",
            title="Software Engineer",
            description="Exciting opportunity for a Python developer...",
            job_type=JobType.FULL_TIME,
            source=JobSource.LINKEDIN,
            company=company,
            location="San Francisco, CA",
            requirements=requirements,
            is_easy_apply=True
        )
        
        assert job.job_id == "test_job_1"
        assert job.title == "Software Engineer"
        assert job.company.name == "Tech Corp"
        assert job.is_easy_apply is True
    
    def test_job_matching(self):
        """Test job matching against user preferences."""
        # Create a job posting
        company = CompanyInfo(name="AI Startup", industry="Technology")
        requirements = JobRequirements(required_skills=["Python", "Machine Learning"])
        
        job = JobPosting(
            job_id="ai_job",
            url="https://example.com/ai-job",
            title="AI Engineer",
            description="Work on cutting-edge AI projects using Python and machine learning.",
            job_type=JobType.FULL_TIME,
            source=JobSource.LINKEDIN,
            company=company,
            location="Remote",
            requirements=requirements
        )
        
        # Create user preferences
        preferences = JobPreferences(
            target_roles=["AI Engineer", "Machine Learning Engineer"],
            experience_levels=[ExperienceLevel.ENTRY_LEVEL],
            preferred_locations=["Remote"],
            required_keywords=["Python", "Machine Learning"]
        )
        
        # Test matching
        match_score = job.calculate_match_score(preferences)
        assert match_score > 0.5  # Should be a good match
        assert "Python" in job.keywords_matched
        assert "Machine Learning" in job.keywords_matched


class TestApplication:
    """Test application functionality."""
    
    def test_create_application(self):
        """Test creating an application."""
        application = Application(
            application_id="app_1",
            job_id="job_1",
            user_id="user_1",
            status=ApplicationStatus.SUBMITTED,
            application_method=ApplicationMethod.EASY_APPLY,
            job_title="Software Engineer",
            company_name="Tech Corp",
            job_url="https://example.com/job/1"
        )
        
        assert application.application_id == "app_1"
        assert application.status == ApplicationStatus.SUBMITTED
        assert application.automated_application is True
    
    def test_application_status_updates(self):
        """Test application status updates."""
        application = Application(
            application_id="app_2",
            job_id="job_2",
            user_id="user_1",
            status=ApplicationStatus.SUBMITTED,
            application_method=ApplicationMethod.EASY_APPLY,
            job_title="Cloud Engineer",
            company_name="Cloud Inc",
            job_url="https://example.com/job/2"
        )
        
        # Test status update
        application.update_status(ApplicationStatus.UNDER_REVIEW, "Application is being reviewed")
        assert application.status == ApplicationStatus.UNDER_REVIEW
        assert len(application.responses) == 1
        
        # Test follow-up logic
        assert not application.needs_follow_up()  # Too recent
    
    def test_notion_properties(self):
        """Test Notion properties generation."""
        application = Application(
            application_id="app_3",
            job_id="job_3",
            user_id="user_1",
            status=ApplicationStatus.SUBMITTED,
            application_method=ApplicationMethod.EASY_APPLY,
            job_title="DevOps Engineer",
            company_name="DevOps Co",
            job_url="https://example.com/job/3"
        )
        
        properties = application.to_notion_properties()
        assert "Job Title" in properties
        assert "Company" in properties
        assert "Status" in properties
        assert properties["Job Title"]["title"][0]["text"]["content"] == "DevOps Engineer"


class TestEncryption:
    """Test encryption functionality."""
    
    def test_encryption_manager(self):
        """Test basic encryption/decryption."""
        manager = EncryptionManager("test_password")
        
        # Test string encryption
        original_text = "This is a secret message"
        encrypted = manager.encrypt(original_text)
        decrypted = manager.decrypt(encrypted)
        
        assert decrypted == original_text
        assert encrypted != original_text
    
    def test_json_encryption(self):
        """Test JSON data encryption."""
        manager = EncryptionManager("test_password")
        
        # Test dictionary encryption
        original_data = {
            "username": "test_user",
            "password": "secret_password",
            "settings": {"theme": "dark", "notifications": True}
        }
        
        encrypted = manager.encrypt(original_data)
        decrypted = manager.decrypt_json(encrypted)
        
        assert decrypted == original_data


class TestSettings:
    """Test settings functionality."""
    
    def test_settings_creation(self):
        """Test creating settings."""
        settings = Settings()
        assert settings.max_applications_per_day == 10
        assert settings.headless_mode is True
        assert settings.log_level == "INFO"
    
    def test_ai_provider_detection(self):
        """Test AI provider detection."""
        # This would need actual API keys to test properly
        # For now, just test the logic
        settings = Settings()
        
        # Mock having OpenAI key
        settings.openai_api_key = "test_key"
        provider = settings.get_ai_provider()
        assert provider == "openai"


@pytest.mark.asyncio
class TestAsyncFunctionality:
    """Test async functionality."""
    
    async def test_async_operations(self):
        """Test basic async operations."""
        # This is a placeholder for async tests
        # In a real implementation, you would test:
        # - Browser automation
        # - AI question answering
        # - Notion API calls
        # - Database operations
        
        await asyncio.sleep(0.1)  # Simulate async operation
        assert True


if __name__ == "__main__":
    pytest.main([__file__])
