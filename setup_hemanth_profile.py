#!/usr/bin/env python3
"""
Automated setup script for <PERSON><PERSON><PERSON>'s Job Auto Applier profile.
This script creates a complete profile using the provided configuration.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from job_auto_applier.models.user_profile import (
    UserProfile, PersonalInfo, JobPreferences, ApplicationAnswers,
    ExperienceLevel, CompanySize, WorkType
)
from job_auto_applier.models.database import get_database_manager
from job_auto_applier.config.encryption import EncryptionManager
from job_auto_applier.config.settings import get_settings


def create_hemanth_profile():
    """Create <PERSON><PERSON><PERSON>'s user profile with the provided configuration."""
    
    print("🚀 Setting up <PERSON><PERSON><PERSON>'s Job Auto Applier profile...")
    
    # Personal Information
    personal_info = PersonalInfo(
        first_name="<PERSON>man<PERSON>",
        last_name="<PERSON>u",
        email="<EMAIL>",
        phone="8408775892",
        location="San Bernardino, CA",
        linkedin_url="https://www.linkedin.com/in/hemanth-kiran-reddy-polu/",
        github_url="https://github.com/hemanthkiran"
    )
    
    # Job Preferences
    job_preferences = JobPreferences(
        target_roles=[
            "Software Engineer",
            "Python Developer", 
            "Full Stack Developer",
            "Backend Developer"
        ],
        experience_levels=[ExperienceLevel.MID_LEVEL, ExperienceLevel.ASSOCIATE],
        preferred_locations=["Remote", "San Bernardino, CA", "Los Angeles, CA"],
        work_types=[WorkType.REMOTE, WorkType.HYBRID],
        company_sizes=[CompanySize.STARTUP, CompanySize.MID_SIZE, CompanySize.LARGE],
        min_salary=80000,
        max_salary=150000,
        currency="USD",
        required_keywords=[
            "Python", "JavaScript", "React", "Django", "Flask",
            "PostgreSQL", "MySQL", "Git", "AWS", "Docker"
        ],
        preferred_keywords=[
            "FastAPI", "Node.js", "TypeScript", "MongoDB", "Redis",
            "Kubernetes", "CI/CD", "Microservices", "REST API"
        ],
        excluded_keywords=["PHP", "Legacy", "COBOL", "Mainframe"],
        max_applications_per_day=10,
        auto_apply_easy_apply=True,
        auto_apply_external=False
    )
    
    # Application Answers
    application_answers = ApplicationAnswers(
        why_interested="""I am passionate about software development and excited about the opportunity to contribute to innovative projects. 
        I'm particularly drawn to roles that allow me to work with modern technologies like Python, React, and cloud platforms while 
        solving complex technical challenges and building scalable solutions.""",
        
        why_good_fit="""My strong background in full-stack development with Python, JavaScript, and modern frameworks makes me well-suited 
        for this role. I have experience building scalable web applications, working with databases, and implementing best practices 
        for code quality and performance. My problem-solving skills and collaborative approach align well with team-oriented environments.""",
        
        career_goals="""I aim to grow as a software engineer by working on challenging projects that have real-world impact. 
        I'm interested in advancing my skills in cloud technologies, microservices architecture, and leading technical initiatives. 
        Long-term, I aspire to take on technical leadership roles while continuing to contribute to innovative software solutions.""",
        
        relevant_experience="""I have experience developing full-stack web applications using Python (Django/Flask), JavaScript (React/Node.js), 
        and working with databases like PostgreSQL and MySQL. I've implemented RESTful APIs, worked with cloud platforms like AWS, 
        and have experience with version control, testing, and deployment practices.""",
        
        biggest_achievement="""Successfully designed and developed a full-stack web application that automated manual processes, 
        resulting in 40% time savings for users. The project involved building a React frontend, Python backend with Django, 
        PostgreSQL database, and deployment on AWS with CI/CD pipeline implementation.""",
        
        technical_skills="""Python (Django, Flask, FastAPI), JavaScript (React, Node.js), TypeScript, HTML/CSS, PostgreSQL, MySQL, 
        MongoDB, Git, AWS (EC2, S3, RDS), Docker, REST APIs, GraphQL, Linux, Agile methodologies, Test-Driven Development, 
        CI/CD pipelines, and experience with modern development tools and practices.""",
        
        available_start_date="2 weeks notice",
        willing_to_relocate=False,
        requires_sponsorship=False,
        
        custom_answers={
            "What interests you about this company?": "I'm impressed by your company's commitment to innovation and the opportunity to work with cutting-edge technologies while contributing to meaningful projects.",
            "Why are you looking for a new opportunity?": "I'm seeking new challenges that will allow me to grow my technical skills and contribute to impactful projects in a collaborative environment.",
            "What's your preferred work environment?": "I thrive in collaborative, remote-friendly environments that value code quality, continuous learning, and innovation.",
            "How do you handle tight deadlines?": "I prioritize tasks effectively, communicate proactively with stakeholders, and focus on delivering high-quality solutions within deadlines.",
            "What motivates you?": "I'm motivated by solving complex technical problems, learning new technologies, and building software that makes a positive impact on users' lives."
        }
    )
    
    # Create the user profile
    user_profile = UserProfile(
        user_id="hemanth_kiran_polu",
        personal_info=personal_info,
        job_preferences=job_preferences,
        application_answers=application_answers,
        linkedin_email="<EMAIL>",
        indeed_email="<EMAIL>",
        glassdoor_email="<EMAIL>",
        notifications_enabled=True,
        auto_apply_enabled=True
    )
    
    return user_profile


def store_credentials():
    """Store encrypted platform credentials."""
    print("🔐 Storing encrypted credentials...")
    
    encryption_manager = EncryptionManager()
    
    # Store LinkedIn credentials
    encryption_manager.store_credential(
        "<EMAIL>", 
        "linkedin", 
        "PHKRmay@2025"
    )
    
    # Store Indeed credentials
    encryption_manager.store_credential(
        "<EMAIL>", 
        "indeed", 
        "PHKRmay@2025"
    )
    
    # Store Glassdoor credentials
    encryption_manager.store_credential(
        "<EMAIL>", 
        "glassdoor", 
        "PHKRmay@2025"
    )
    
    print("✅ Credentials stored securely")


def main():
    """Main setup function."""
    try:
        print("🎯 Job Auto Applier - Hemanth's Profile Setup")
        print("=" * 50)
        
        # Create directories
        os.makedirs("logs", exist_ok=True)
        os.makedirs("screenshots", exist_ok=True)
        os.makedirs("cache", exist_ok=True)
        
        # Initialize database
        print("📊 Initializing database...")
        db_manager = get_database_manager()
        
        # Create user profile
        print("👤 Creating user profile...")
        user_profile = create_hemanth_profile()
        
        # Store credentials
        store_credentials()
        
        # Save profile to database
        print("💾 Saving profile to database...")
        db_manager.save_user_profile(user_profile)
        
        print("\n✅ Setup completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Install browser: playwright install chromium --with-deps")
        print("3. Test the system: python -m job_auto_applier.cli.main test")
        print("4. Run a dry run: python -m job_auto_applier.cli.main run --dry-run")
        print("5. Start applying: python -m job_auto_applier.cli.main run")
        
        print("\n🎉 Your Job Auto Applier is ready!")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
