#!/bin/bash

# Job Auto Applier Installation Script
# This script sets up the Job Auto Applier system

set -e

echo "🚀 Job Auto Applier Installation Script"
echo "======================================="

# Check Python version
echo "📋 Checking Python version..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.11 or higher is required. Found: $python_version"
    echo "Please install Python 3.11+ and try again."
    exit 1
fi

echo "✅ Python version: $python_version"

# Create virtual environment
echo "🔧 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Upgrade pip
echo "📦 Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Install playwright browsers
echo "🌐 Installing browser automation..."
playwright install chromium --with-deps

# Install the package in development mode
echo "📦 Installing Job Auto Applier..."
pip install -e .

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p screenshots
mkdir -p cache

# Set up environment file
if [ ! -f .env ]; then
    echo "⚙️ Creating environment file..."
    cp .env.example .env
    echo "📝 Please edit .env file with your API keys and configuration"
fi

# Make CLI executable
echo "🔗 Setting up CLI..."
chmod +x job_auto_applier/cli/main.py

echo ""
echo "✅ Installation completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Edit the .env file with your API keys:"
echo "   - OpenAI or Anthropic API key"
echo "   - Notion API key and database ID"
echo ""
echo "2. Run the setup command:"
echo "   source venv/bin/activate"
echo "   job-applier setup"
echo ""
echo "3. Start applying to jobs:"
echo "   job-applier run"
echo ""
echo "4. Check application status:"
echo "   job-applier status"
echo ""
echo "📚 For more information, see README.md"
echo ""
echo "🎉 Happy job hunting!"
