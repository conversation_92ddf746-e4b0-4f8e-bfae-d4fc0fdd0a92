#!/bin/bash

# Job Auto Applier - Quick Start for Hemanth
# ==========================================

set -e

echo "🚀 Job Auto Applier - Quick Start Setup"
echo "======================================="
echo "Setting up for: <PERSON><PERSON><PERSON>"
echo ""

# Check Python version
echo "📋 Checking Python version..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.11 or higher is required. Found: $python_version"
    echo "Please install Python 3.11+ and try again."
    exit 1
fi

echo "✅ Python version: $python_version"

# Create virtual environment
echo "🔧 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Upgrade pip
echo "📦 Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Install playwright browsers
echo "🌐 Installing browser automation..."
playwright install chromium --with-deps

# Install the package in development mode
echo "📦 Installing Job Auto Applier..."
pip install -e .

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p screenshots
mkdir -p cache

# Run the personalized setup
echo "👤 Setting up Hemanth's profile..."
python setup_hemanth_profile.py

# Test the installation
echo "🧪 Testing installation..."
python -m job_auto_applier.cli.main test

echo ""
echo "✅ Quick start completed successfully!"
echo ""
echo "🎯 Your Job Auto Applier is ready to use!"
echo ""
echo "📋 Available Commands:"
echo "  source venv/bin/activate                    # Activate environment"
echo "  python -m job_auto_applier.cli.main run --dry-run  # Test run (no applications)"
echo "  python -m job_auto_applier.cli.main run            # Start applying to jobs"
echo "  python -m job_auto_applier.cli.main status         # Check application status"
echo ""
echo "🔧 Configuration:"
echo "  Profile: hemanth_kiran_polu"
echo "  Target Roles: Software Engineer, Python Developer, Full Stack Developer"
echo "  Location: Remote preferred"
echo "  Salary Range: $80,000 - $150,000"
echo "  Max Applications/Day: 10"
echo ""
echo "🔒 Security:"
echo "  ✅ Credentials encrypted and stored securely"
echo "  ✅ Personal data protected with encryption"
echo "  ✅ Local database for application tracking"
echo ""
echo "📊 Optional: Set up Notion integration for advanced tracking"
echo "  1. Create a Notion integration at https://www.notion.so/my-integrations"
echo "  2. Create a database with the required properties"
echo "  3. Add NOTION_API_KEY and NOTION_DATABASE_ID to .env file"
echo ""
echo "🎉 Happy job hunting, Hemanth!"
echo ""
echo "💡 Pro Tips:"
echo "  - Start with a dry run to see what jobs would be found"
echo "  - Monitor the logs in the logs/ directory"
echo "  - Screenshots are saved in screenshots/ for debugging"
echo "  - Check status regularly to track your applications"
echo ""
