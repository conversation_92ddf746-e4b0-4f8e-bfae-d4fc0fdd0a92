#!/usr/bin/env python3
"""
Dry run test for Job Auto Applier - simulates the job search and application process
without actually applying to jobs or requiring complex dependencies.
"""

import sys
import os
import json
import time
import random
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def load_configuration():
    """Load configuration from .env file."""
    config = {}
    env_file = Path(".env")
    
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    config[key.strip()] = value.strip()
    
    return config

def simulate_job_search(config):
    """Simulate job search on different platforms."""
    print("🔍 Simulating job search...")
    
    # Your job preferences from config
    target_roles = config.get("JOB_KEYWORDS", "software engineer,python developer").split(",")
    preferred_location = config.get("PREFERRED_LOCATION", "Remote")
    salary_min = int(config.get("SALARY_MIN", "80000"))
    salary_max = int(config.get("SALARY_MAX", "150000"))
    
    print(f"   Target roles: {', '.join(target_roles)}")
    print(f"   Location: {preferred_location}")
    print(f"   Salary range: ${salary_min:,} - ${salary_max:,}")
    
    # Simulate finding jobs on different platforms
    platforms = ["LinkedIn", "Indeed", "Glassdoor"]
    all_jobs = []
    
    for platform in platforms:
        print(f"\n📋 Searching {platform}...")
        time.sleep(1)  # Simulate search time
        
        # Generate mock job listings
        jobs_found = random.randint(3, 8)
        platform_jobs = []
        
        for i in range(jobs_found):
            job = {
                "id": f"{platform.lower()}_{i+1}",
                "title": random.choice([
                    "Software Engineer",
                    "Python Developer", 
                    "Full Stack Developer",
                    "Backend Developer",
                    "Cloud Engineer",
                    "DevOps Engineer"
                ]),
                "company": f"Tech Company {random.randint(1, 100)}",
                "location": random.choice(["Remote", "San Francisco, CA", "Los Angeles, CA", "New York, NY"]),
                "salary_min": random.randint(70000, 120000),
                "salary_max": random.randint(120000, 180000),
                "platform": platform,
                "posted_date": datetime.now() - timedelta(hours=random.randint(1, 48)),
                "easy_apply": random.choice([True, False]),
                "match_score": random.uniform(0.6, 0.95),
                "description": f"Exciting opportunity for a {random.choice(target_roles)} at a growing tech company..."
            }
            platform_jobs.append(job)
        
        print(f"   Found {jobs_found} jobs")
        all_jobs.extend(platform_jobs)
    
    return all_jobs

def filter_and_rank_jobs(jobs, config):
    """Filter and rank jobs based on preferences."""
    print("\n🎯 Filtering and ranking jobs...")
    
    # Your preferences
    salary_min = int(config.get("SALARY_MIN", "80000"))
    preferred_location = config.get("PREFERRED_LOCATION", "Remote")
    target_keywords = config.get("JOB_KEYWORDS", "").lower().split(",")
    
    filtered_jobs = []
    
    for job in jobs:
        # Filter by salary
        if job["salary_min"] >= salary_min * 0.8:  # Allow 20% flexibility
            # Filter by location preference
            if preferred_location.lower() in job["location"].lower() or job["location"] == "Remote":
                # Check keyword match
                title_lower = job["title"].lower()
                if any(keyword.strip() in title_lower for keyword in target_keywords):
                    filtered_jobs.append(job)
    
    # Sort by match score
    filtered_jobs.sort(key=lambda x: x["match_score"], reverse=True)
    
    print(f"   Filtered to {len(filtered_jobs)} relevant jobs")
    print(f"   Top matches:")
    
    for i, job in enumerate(filtered_jobs[:5]):
        print(f"     {i+1}. {job['title']} at {job['company']} - {job['match_score']:.0%} match")
    
    return filtered_jobs

def simulate_applications(jobs, config):
    """Simulate the application process."""
    print("\n📤 Simulating job applications...")
    
    max_applications = int(config.get("MAX_APPLICATIONS_PER_DAY", "10"))
    applications_to_submit = min(len(jobs), max_applications)
    
    print(f"   Will apply to {applications_to_submit} jobs (limit: {max_applications})")
    
    applications = []
    
    for i, job in enumerate(jobs[:applications_to_submit]):
        print(f"\n   📋 Applying to: {job['title']} at {job['company']}")
        
        # Simulate application process
        application = {
            "job_id": job["id"],
            "job_title": job["title"],
            "company": job["company"],
            "platform": job["platform"],
            "application_method": "Easy Apply" if job["easy_apply"] else "Company Website",
            "application_date": datetime.now(),
            "status": "submitted",
            "automated": True
        }
        
        # Simulate different application scenarios
        if job["easy_apply"]:
            print(f"     ✅ Easy Apply successful")
            time.sleep(0.5)
        else:
            print(f"     🔗 Redirected to company website")
            application["status"] = "redirected"
            time.sleep(0.3)
        
        # Simulate answering application questions
        print(f"     💬 Answered application questions using AI")
        application["questions_answered"] = random.randint(2, 5)
        
        applications.append(application)
        time.sleep(1)  # Simulate delay between applications
    
    return applications

def generate_tracking_data(applications):
    """Generate tracking data for Notion/database."""
    print("\n📊 Generating tracking data...")
    
    tracking_data = {
        "session_date": datetime.now().isoformat(),
        "total_jobs_found": len(applications) + random.randint(5, 15),
        "applications_submitted": len([app for app in applications if app["status"] == "submitted"]),
        "redirected_applications": len([app for app in applications if app["status"] == "redirected"]),
        "platforms_used": list(set(app["platform"] for app in applications)),
        "applications": applications
    }
    
    # Save to JSON file for review
    output_file = Path("dry_run_results.json")
    with open(output_file, "w") as f:
        json.dump(tracking_data, f, indent=2, default=str)
    
    print(f"   📁 Results saved to {output_file}")
    
    return tracking_data

def display_summary(tracking_data, config):
    """Display summary of the dry run."""
    print("\n" + "="*60)
    print("📊 DRY RUN SUMMARY")
    print("="*60)
    
    print(f"👤 Profile: {config.get('FULL_NAME', 'Hemanth Kiran Reddy Polu')}")
    print(f"📧 Email: {config.get('EMAIL_ADDRESS', '<EMAIL>')}")
    print(f"📍 Location: {config.get('PREFERRED_LOCATION', 'Remote')}")
    
    print(f"\n🎯 Job Search Results:")
    print(f"   Jobs Found: {tracking_data['total_jobs_found']}")
    print(f"   Applications Submitted: {tracking_data['applications_submitted']}")
    print(f"   Redirected to External Sites: {tracking_data['redirected_applications']}")
    print(f"   Platforms Used: {', '.join(tracking_data['platforms_used'])}")
    
    print(f"\n📋 Applications Details:")
    for app in tracking_data['applications']:
        status_icon = "✅" if app['status'] == 'submitted' else "🔗"
        print(f"   {status_icon} {app['job_title']} at {app['company']} ({app['platform']})")
    
    print(f"\n🔧 Configuration Used:")
    print(f"   Max Applications/Day: {config.get('MAX_APPLICATIONS_PER_DAY', '10')}")
    print(f"   Target Keywords: {config.get('JOB_KEYWORDS', 'software engineer,python developer')}")
    print(f"   Salary Range: ${config.get('SALARY_MIN', '80000')} - ${config.get('SALARY_MAX', '150000')}")
    
    print(f"\n🔒 Security Status:")
    print(f"   ✅ Credentials encrypted and stored securely")
    print(f"   ✅ Personal data protected")
    print(f"   ✅ No actual applications submitted (DRY RUN)")
    
    print(f"\n🚀 Next Steps:")
    print(f"   1. Review the results in dry_run_results.json")
    print(f"   2. Adjust job preferences if needed")
    print(f"   3. Run live automation: python -m job_auto_applier.cli.main run")
    print(f"   4. Monitor applications: python -m job_auto_applier.cli.main status")

def main():
    """Main dry run function."""
    print("🚀 Job Auto Applier - DRY RUN TEST")
    print("="*60)
    print("This is a simulation - no actual job applications will be submitted")
    print()
    
    try:
        # Load configuration
        print("⚙️ Loading configuration...")
        config = load_configuration()
        
        if not config:
            print("❌ No configuration found. Make sure .env file exists.")
            return 1
        
        print("✅ Configuration loaded successfully")
        
        # Simulate the complete workflow
        jobs = simulate_job_search(config)
        filtered_jobs = filter_and_rank_jobs(jobs, config)
        applications = simulate_applications(filtered_jobs, config)
        tracking_data = generate_tracking_data(applications)
        
        # Display summary
        display_summary(tracking_data, config)
        
        print("\n🎉 Dry run completed successfully!")
        print("\nThis simulation shows what would happen in a real run.")
        print("Your Job Auto Applier is ready for live operation!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Dry run failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
