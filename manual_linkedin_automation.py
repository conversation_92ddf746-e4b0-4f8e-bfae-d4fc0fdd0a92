#!/usr/bin/env python3
"""
Manual LinkedIn Job Automation - Works without AI APIs.
Uses pre-written professional responses and Selenium automation.
"""

import time
import json
import logging
import random
from pathlib import Path
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/manual_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """Load configuration from .env file."""
    config = {}
    env_file = Path(".env")
    
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    config[key.strip()] = value.strip()
    
    return config

class ManualLinkedInAutomation:
    """LinkedIn automation with pre-written responses."""
    
    def __init__(self, config):
        self.config = config
        self.driver = None
        self.applications_today = 0
        self.max_applications = int(config.get('MAX_APPLICATIONS_PER_DAY', '10'))
        
        # Pre-written professional responses
        self.responses = {
            "cover_letter": """I am excited about this software engineering opportunity and believe my skills align well with your team's needs. With experience in Python, JavaScript, and full-stack development, I am confident I can contribute effectively to your projects. I am passionate about building scalable applications and working with modern technologies to solve complex challenges.""",
            
            "why_interested": """I am passionate about software development and excited about the opportunity to work with innovative technologies. My experience with Python, React, and cloud platforms aligns well with this role, and I am eager to contribute to meaningful projects while continuing to grow my technical skills in a collaborative environment.""",
            
            "why_good_fit": """My strong background in full-stack development with Python, JavaScript, and modern frameworks makes me well-suited for this role. I have experience building scalable web applications, working with databases, implementing best practices for code quality and performance, and collaborating effectively in agile development environments.""",
            
            "technical_skills": """Python (Django, Flask, FastAPI), JavaScript (React, Node.js), TypeScript, HTML/CSS, PostgreSQL, MySQL, MongoDB, Git, AWS (EC2, S3, RDS), Docker, REST APIs, GraphQL, Linux, Agile methodologies, Test-Driven Development, CI/CD pipelines, and experience with modern development tools.""",
            
            "career_goals": """I aim to grow as a software engineer by working on challenging projects that have real-world impact. I'm interested in advancing my skills in cloud technologies, microservices architecture, and leading technical initiatives while contributing to innovative solutions.""",
            
            "availability": """I am available to start immediately and am excited about the opportunity to contribute to your team. I am flexible with scheduling and committed to delivering high-quality work."""
        }
    
    def setup_driver(self):
        """Setup Chrome WebDriver."""
        logger.info("Setting up Chrome WebDriver...")
        
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.maximize_window()
            logger.info("Chrome WebDriver setup successful")
            return True
        except Exception as e:
            logger.error(f"Failed to setup Chrome WebDriver: {e}")
            return False
    
    def login_to_linkedin(self):
        """Login to LinkedIn."""
        logger.info("Logging into LinkedIn...")
        
        try:
            self.driver.get("https://www.linkedin.com/login")
            time.sleep(3)
            
            email = self.config.get('LINKEDIN_EMAIL')
            password = self.config.get('LINKEDIN_PASSWORD', 'PHKRmay@2025')
            
            # Enter email
            email_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            email_field.clear()
            email_field.send_keys(email)
            
            # Enter password
            password_field = self.driver.find_element(By.ID, "password")
            password_field.clear()
            password_field.send_keys(password)
            
            # Submit
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            time.sleep(5)
            
            # Check if logged in
            current_url = self.driver.current_url
            if "feed" in current_url or "mynetwork" in current_url or "linkedin.com/in/" in current_url:
                logger.info("LinkedIn login successful")
                return True
            else:
                logger.error(f"LinkedIn login failed. Current URL: {current_url}")
                return False
                
        except Exception as e:
            logger.error(f"LinkedIn login error: {e}")
            return False
    
    def search_jobs(self):
        """Search for jobs using direct URL."""
        logger.info("Searching for jobs...")
        
        try:
            keywords = self.config.get('JOB_KEYWORDS', 'software engineer').replace(',', '%20')
            location = self.config.get('PREFERRED_LOCATION', 'Remote').replace(' ', '%20')
            
            # Direct search URL
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keywords}&location={location}&f_AL=true&f_TPR=r86400"
            
            logger.info(f"Navigating to: {search_url}")
            self.driver.get(search_url)
            time.sleep(5)
            
            # Wait for results
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-search-results-list"))
            )
            
            # Find job cards
            job_selectors = [
                ".job-card-container",
                ".jobs-search-results__list-item",
                "[data-job-id]"
            ]
            
            job_cards = []
            for selector in job_selectors:
                try:
                    cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if cards:
                        job_cards = cards
                        break
                except:
                    continue
            
            logger.info(f"Found {len(job_cards)} job listings")
            return job_cards[:10]
            
        except Exception as e:
            logger.error(f"Job search error: {e}")
            return []
    
    def apply_to_job(self, job_card, index):
        """Apply to a specific job."""
        try:
            # Scroll and click
            self.driver.execute_script("arguments[0].scrollIntoView(true);", job_card)
            time.sleep(2)
            job_card.click()
            time.sleep(3)
            
            # Get job details
            job_title = "Software Engineering Position"
            company = "Tech Company"
            
            try:
                title_selectors = [
                    "h1.job-title",
                    ".job-details-jobs-unified-top-card__job-title",
                    "h1"
                ]
                
                for selector in title_selectors:
                    try:
                        title_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        job_title = title_element.text.strip()
                        if job_title:
                            break
                    except:
                        continue
                
                company_selectors = [
                    ".job-details-jobs-unified-top-card__company-name",
                    ".job-details-jobs-unified-top-card__primary-description"
                ]
                
                for selector in company_selectors:
                    try:
                        company_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        company = company_element.text.strip()
                        if company:
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not extract job details: {e}")
            
            logger.info(f"Processing: {job_title} at {company}")
            
            # Look for Easy Apply
            easy_apply_selectors = [
                "button[aria-label*='Easy Apply']",
                ".jobs-apply-button",
                "[data-control-name='jobdetails_topcard_inapply']"
            ]
            
            easy_apply_button = None
            for selector in easy_apply_selectors:
                try:
                    easy_apply_button = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue
            
            if easy_apply_button:
                logger.info("Found Easy Apply button, clicking...")
                easy_apply_button.click()
                time.sleep(3)
                
                if self.handle_application_form():
                    self.applications_today += 1
                    logger.info(f"Successfully applied to {job_title} at {company}")
                    
                    return {
                        "job_title": job_title,
                        "company": company,
                        "status": "applied",
                        "method": "Easy Apply",
                        "timestamp": datetime.now().isoformat(),
                        "platform": "LinkedIn"
                    }
                else:
                    logger.warning(f"Failed to complete application for {job_title}")
                    return None
            else:
                logger.info(f"No Easy Apply available for {job_title}")
                return None
                
        except Exception as e:
            logger.error(f"Error applying to job: {e}")
            return None
    
    def handle_application_form(self):
        """Handle Easy Apply form with pre-written responses."""
        try:
            max_steps = 5
            
            for step in range(max_steps):
                # Fill phone number
                try:
                    phone_fields = self.driver.find_elements(By.CSS_SELECTOR, "input[type='tel']")
                    for field in phone_fields:
                        if not field.get_attribute('value'):
                            field.send_keys(self.config.get('PHONE_NUMBER', '8408775892'))
                except:
                    pass
                
                # Fill text areas with appropriate responses
                try:
                    text_areas = self.driver.find_elements(By.CSS_SELECTOR, "textarea")
                    for textarea in text_areas:
                        if not textarea.get_attribute('value'):
                            placeholder = textarea.get_attribute('placeholder') or ''
                            label_text = ''
                            
                            # Try to find associated label
                            try:
                                label_id = textarea.get_attribute('aria-labelledby')
                                if label_id:
                                    label = self.driver.find_element(By.ID, label_id)
                                    label_text = label.text.lower()
                            except:
                                pass
                            
                            combined_text = (placeholder + ' ' + label_text).lower()
                            
                            if any(word in combined_text for word in ['cover', 'letter', 'message']):
                                textarea.send_keys(self.responses['cover_letter'])
                            elif any(word in combined_text for word in ['why', 'interested', 'motivation']):
                                textarea.send_keys(self.responses['why_interested'])
                            elif any(word in combined_text for word in ['fit', 'qualified', 'suitable']):
                                textarea.send_keys(self.responses['why_good_fit'])
                            elif any(word in combined_text for word in ['skill', 'experience', 'technical']):
                                textarea.send_keys(self.responses['technical_skills'])
                            else:
                                textarea.send_keys(self.responses['why_interested'])
                except:
                    pass
                
                # Handle dropdowns
                try:
                    selects = self.driver.find_elements(By.CSS_SELECTOR, "select")
                    for select in selects:
                        options = select.find_elements(By.TAG_NAME, "option")
                        if len(options) > 1:
                            # Select appropriate option based on context
                            select_text = select.get_attribute('aria-label') or ''
                            if 'experience' in select_text.lower():
                                # Select mid-level experience
                                for option in options:
                                    if any(word in option.text.lower() for word in ['3-5', 'mid', '2-4']):
                                        option.click()
                                        break
                                else:
                                    options[min(2, len(options)-1)].click()
                            else:
                                options[1].click()
                except:
                    pass
                
                # Look for submit button
                submit_selectors = [
                    "button[aria-label*='Submit']",
                    "button:contains('Submit')",
                    ".artdeco-button--primary"
                ]
                
                submitted = False
                for selector in submit_selectors:
                    try:
                        submit_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if "submit" in submit_btn.text.lower():
                            submit_btn.click()
                            time.sleep(3)
                            submitted = True
                            break
                    except:
                        continue
                
                if submitted:
                    return True
                
                # Look for next button
                next_selectors = [
                    "button[aria-label*='Continue']",
                    "button:contains('Next')",
                    "button:contains('Continue')"
                ]
                
                found_next = False
                for selector in next_selectors:
                    try:
                        next_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        next_btn.click()
                        time.sleep(2)
                        found_next = True
                        break
                    except:
                        continue
                
                if not found_next:
                    break
            
            return True
            
        except Exception as e:
            logger.error(f"Error in application form: {e}")
            return False
    
    def close_driver(self):
        """Close the WebDriver."""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

def run_manual_automation():
    """Run manual LinkedIn automation."""
    print("🚀 MANUAL LINKEDIN JOB AUTOMATION")
    print("=" * 50)
    print("Uses pre-written professional responses (no AI required)")
    print()
    
    config = load_config()
    if not config:
        print("❌ Configuration not found")
        return
    
    print(f"👤 Profile: {config.get('FULL_NAME', 'Hemanth Kiran Reddy Polu')}")
    print(f"🔗 LinkedIn: {config.get('LINKEDIN_EMAIL', 'Not configured')}")
    print(f"🎯 Keywords: {config.get('JOB_KEYWORDS', 'software engineer')}")
    print(f"📍 Location: {config.get('PREFERRED_LOCATION', 'Remote')}")
    print(f"📊 Max Apps: {config.get('MAX_APPLICATIONS_PER_DAY', '10')}")
    print()
    
    confirm = input("Start REAL LinkedIn applications? (YES/no): ").strip()
    if confirm.upper() != 'YES':
        print("❌ Cancelled")
        return
    
    automation = ManualLinkedInAutomation(config)
    applications = []
    
    try:
        if not automation.setup_driver():
            print("❌ Browser setup failed")
            return
        
        print("✅ Browser ready")
        
        if not automation.login_to_linkedin():
            print("❌ Login failed")
            return
        
        print("✅ Logged into LinkedIn")
        
        job_cards = automation.search_jobs()
        if not job_cards:
            print("❌ No jobs found")
            return
        
        print(f"✅ Found {len(job_cards)} jobs")
        
        for i, job_card in enumerate(job_cards):
            if automation.applications_today >= automation.max_applications:
                print(f"⚠️ Daily limit reached")
                break
            
            print(f"\n📝 Job {i+1}/{len(job_cards)}...")
            
            application = automation.apply_to_job(job_card, i)
            if application:
                applications.append(application)
                print(f"✅ Applied! Total: {len(applications)}")
            
            time.sleep(random.randint(10, 20))
        
        # Save results
        results = {
            "session_date": datetime.now().isoformat(),
            "total_applications": len(applications),
            "applications": applications,
            "automation_method": "manual_responses"
        }
        
        with open("manual_linkedin_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n🎉 COMPLETE! Applied to {len(applications)} jobs")
        for app in applications:
            print(f"✅ {app['job_title']} at {app['company']}")
        
    except Exception as e:
        logger.error(f"Automation error: {e}")
        print(f"❌ Error: {e}")
    
    finally:
        automation.close_driver()

if __name__ == "__main__":
    Path("logs").mkdir(exist_ok=True)
    run_manual_automation()
