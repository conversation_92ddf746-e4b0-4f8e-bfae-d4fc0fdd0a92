#!/usr/bin/env python3
"""
REAL Job Auto Applier - Uses actual browser automation to apply to real jobs.
This script uses <PERSON><PERSON> to automate real job applications.
"""

import asyncio
import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime
from playwright.async_api import async_playwright

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/job_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """Load configuration from .env file."""
    config = {}
    env_file = Path(".env")
    
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    config[key.strip()] = value.strip()
    
    return config

class LinkedInAutomation:
    """Real LinkedIn automation using Playwright."""
    
    def __init__(self, config):
        self.config = config
        self.browser = None
        self.page = None
        self.applications_today = 0
        self.max_applications = int(config.get('MAX_APPLICATIONS_PER_DAY', '10'))
        
    async def initialize_browser(self):
        """Initialize browser with Playwright."""
        logger.info("Initializing browser...")
        
        playwright = await async_playwright().start()
        
        # Launch browser (non-headless for first run to handle any CAPTCHAs)
        self.browser = await playwright.chromium.launch(
            headless=False,  # Set to True after testing
            slow_mo=1000,    # Slow down actions to appear more human
        )
        
        # Create new page
        self.page = await self.browser.new_page()
        
        # Set user agent to appear more human
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        logger.info("Browser initialized successfully")
    
    async def login_to_linkedin(self):
        """Login to LinkedIn."""
        logger.info("Logging into LinkedIn...")
        
        try:
            # Navigate to LinkedIn login
            await self.page.goto("https://www.linkedin.com/login")
            await self.page.wait_for_load_state("networkidle")
            
            # Fill login credentials
            email = self.config.get('LINKEDIN_EMAIL')
            password = self.config.get('LINKEDIN_PASSWORD', 'PHKRmay@2025')  # From your config
            
            await self.page.fill('input[name="session_key"]', email)
            await self.page.fill('input[name="session_password"]', password)
            
            # Click login button
            await self.page.click('button[type="submit"]')
            await self.page.wait_for_load_state("networkidle")
            
            # Check if login was successful
            current_url = self.page.url
            if "feed" in current_url or "mynetwork" in current_url:
                logger.info("LinkedIn login successful")
                return True
            else:
                logger.error("LinkedIn login failed - may need manual intervention")
                return False
                
        except Exception as e:
            logger.error(f"LinkedIn login error: {e}")
            return False
    
    async def search_jobs(self):
        """Search for jobs on LinkedIn."""
        logger.info("Searching for jobs on LinkedIn...")
        
        try:
            # Navigate to jobs page
            await self.page.goto("https://www.linkedin.com/jobs/")
            await self.page.wait_for_load_state("networkidle")
            
            # Search for jobs
            keywords = self.config.get('JOB_KEYWORDS', 'software engineer').replace(',', ' ')
            location = self.config.get('PREFERRED_LOCATION', 'Remote')
            
            # Fill search fields
            await self.page.fill('input[aria-label="Search by title, skill, or company"]', keywords)
            await self.page.fill('input[aria-label="City, state, or zip code"]', location)
            
            # Click search
            await self.page.click('button[aria-label="Search"]')
            await self.page.wait_for_load_state("networkidle")
            
            # Apply filters for Easy Apply
            try:
                await self.page.click('button[aria-label="Easy Apply filter."]')
                await self.page.wait_for_timeout(2000)
            except:
                logger.info("Easy Apply filter not found, continuing without it")
            
            # Get job listings
            job_cards = await self.page.query_selector_all('.job-card-container')
            logger.info(f"Found {len(job_cards)} job listings")
            
            return job_cards[:10]  # Limit to first 10 jobs
            
        except Exception as e:
            logger.error(f"Job search error: {e}")
            return []
    
    async def apply_to_job(self, job_card):
        """Apply to a specific job."""
        try:
            # Click on the job card
            await job_card.click()
            await self.page.wait_for_timeout(3000)
            
            # Get job title and company
            job_title = await self.page.text_content('h1.job-title')
            company = await self.page.text_content('.job-details-jobs-unified-top-card__company-name')
            
            logger.info(f"Applying to: {job_title} at {company}")
            
            # Look for Easy Apply button
            easy_apply_button = await self.page.query_selector('button[aria-label*="Easy Apply"]')
            
            if easy_apply_button:
                await easy_apply_button.click()
                await self.page.wait_for_timeout(2000)
                
                # Handle the Easy Apply process
                success = await self.handle_easy_apply_form()
                
                if success:
                    self.applications_today += 1
                    logger.info(f"Successfully applied to {job_title} at {company}")
                    
                    return {
                        "job_title": job_title,
                        "company": company,
                        "status": "applied",
                        "method": "Easy Apply",
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"Failed to complete application for {job_title}")
                    return None
            else:
                logger.info(f"No Easy Apply available for {job_title} - skipping")
                return None
                
        except Exception as e:
            logger.error(f"Error applying to job: {e}")
            return None
    
    async def handle_easy_apply_form(self):
        """Handle the Easy Apply form submission."""
        try:
            max_attempts = 5
            current_attempt = 0
            
            while current_attempt < max_attempts:
                current_attempt += 1
                
                # Check for Next button
                next_button = await self.page.query_selector('button[aria-label="Continue to next step"]')
                if not next_button:
                    next_button = await self.page.query_selector('button:has-text("Next")')
                
                # Check for Submit button
                submit_button = await self.page.query_selector('button[aria-label="Submit application"]')
                if not submit_button:
                    submit_button = await self.page.query_selector('button:has-text("Submit")')
                
                # Fill any visible form fields
                await self.fill_form_fields()
                
                if submit_button:
                    # Final submission
                    await submit_button.click()
                    await self.page.wait_for_timeout(3000)
                    
                    # Check for success message
                    success_indicators = [
                        'text="Application sent"',
                        'text="Your application was submitted"',
                        '[data-test-modal-id="application-submitted-modal"]'
                    ]
                    
                    for indicator in success_indicators:
                        if await self.page.query_selector(indicator):
                            return True
                    
                    return True  # Assume success if no error
                    
                elif next_button:
                    # Continue to next step
                    await next_button.click()
                    await self.page.wait_for_timeout(2000)
                else:
                    # No more buttons, might be done
                    break
            
            return False
            
        except Exception as e:
            logger.error(f"Error in Easy Apply form: {e}")
            return False
    
    async def fill_form_fields(self):
        """Fill common form fields."""
        try:
            # Phone number
            phone_inputs = await self.page.query_selector_all('input[type="tel"]')
            for phone_input in phone_inputs:
                await phone_input.fill(self.config.get('PHONE_NUMBER', ''))
            
            # Text areas for questions
            text_areas = await self.page.query_selector_all('textarea')
            for textarea in text_areas:
                placeholder = await textarea.get_attribute('placeholder')
                if placeholder:
                    if 'cover letter' in placeholder.lower():
                        await textarea.fill(self.get_cover_letter_response())
                    elif 'why' in placeholder.lower():
                        await textarea.fill(self.get_why_interested_response())
            
            # Dropdowns for experience, etc.
            selects = await self.page.query_selector_all('select')
            for select in selects:
                options = await select.query_selector_all('option')
                if len(options) > 1:
                    # Select the second option (usually "Yes" or appropriate choice)
                    await select.select_option(index=1)
            
        except Exception as e:
            logger.error(f"Error filling form fields: {e}")
    
    def get_cover_letter_response(self):
        """Get cover letter response."""
        return """I am excited about this opportunity to contribute to your team as a software engineer. With my strong background in Python, JavaScript, and full-stack development, I am confident I can make a valuable contribution to your projects. I am particularly drawn to roles that allow me to work with modern technologies and solve complex technical challenges."""
    
    def get_why_interested_response(self):
        """Get why interested response."""
        return """I am passionate about software development and excited about the opportunity to work with innovative technologies. My experience with Python, React, and cloud platforms aligns well with this role, and I am eager to contribute to meaningful projects while continuing to grow my technical skills."""
    
    async def close_browser(self):
        """Close the browser."""
        if self.browser:
            await self.browser.close()
            logger.info("Browser closed")

async def run_real_automation():
    """Run the real job automation."""
    print("🚀 REAL JOB AUTO APPLIER - STARTING")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    if not config:
        print("❌ Configuration not found")
        return
    
    print(f"Profile: {config.get('FULL_NAME', 'Hemanth Kiran Reddy Polu')}")
    print(f"Target: {config.get('JOB_KEYWORDS', 'software engineer')}")
    print(f"Location: {config.get('PREFERRED_LOCATION', 'Remote')}")
    print(f"Max Applications: {config.get('MAX_APPLICATIONS_PER_DAY', '10')}")
    print()
    
    # Confirm before starting
    print("⚠️ WARNING: This will apply to REAL jobs on LinkedIn!")
    print("Make sure your resume and profile are up to date.")
    print()
    
    confirm = input("Do you want to proceed with REAL applications? (yes/no): ").lower().strip()
    if confirm not in ['yes', 'y']:
        print("❌ Automation cancelled.")
        return
    
    # Initialize automation
    linkedin = LinkedInAutomation(config)
    applications = []
    
    try:
        # Start browser
        await linkedin.initialize_browser()
        
        # Login to LinkedIn
        if not await linkedin.login_to_linkedin():
            print("❌ LinkedIn login failed. Please check credentials.")
            return
        
        print("✅ Successfully logged into LinkedIn")
        
        # Search for jobs
        job_cards = await linkedin.search_jobs()
        
        if not job_cards:
            print("❌ No jobs found matching your criteria")
            return
        
        print(f"✅ Found {len(job_cards)} jobs to review")
        
        # Apply to jobs
        for i, job_card in enumerate(job_cards):
            if linkedin.applications_today >= linkedin.max_applications:
                print(f"⚠️ Daily limit reached ({linkedin.max_applications} applications)")
                break
            
            print(f"\n📝 Processing job {i+1}/{len(job_cards)}...")
            
            application = await linkedin.apply_to_job(job_card)
            if application:
                applications.append(application)
                print(f"✅ Application #{len(applications)} submitted")
            
            # Delay between applications
            await asyncio.sleep(10)  # 10 second delay
        
        # Save results
        results = {
            "session_date": datetime.now().isoformat(),
            "total_applications": len(applications),
            "applications": applications,
            "config_used": {
                "keywords": config.get('JOB_KEYWORDS'),
                "location": config.get('PREFERRED_LOCATION'),
                "max_daily": config.get('MAX_APPLICATIONS_PER_DAY')
            }
        }
        
        results_file = Path("real_applications.json")
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📊 SESSION COMPLETE")
        print(f"Applications submitted: {len(applications)}")
        print(f"Results saved to: {results_file}")
        
        for app in applications:
            print(f"✅ {app['job_title']} at {app['company']}")
        
    except Exception as e:
        logger.error(f"Automation error: {e}")
        print(f"❌ Error: {e}")
    
    finally:
        await linkedin.close_browser()

if __name__ == "__main__":
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Run the automation
    asyncio.run(run_real_automation())
