#!/usr/bin/env python3
"""
Simple test script to verify basic functionality without complex dependencies.
"""

import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_basic_imports():
    """Test basic Python imports."""
    print("🧪 Testing basic imports...")
    
    try:
        import json
        import sqlite3
        import asyncio
        from pathlib import Path
        print("✅ Basic Python modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Basic import failed: {e}")
        return False

def test_environment_loading():
    """Test environment variable loading."""
    print("🔧 Testing environment loading...")
    
    try:
        # Test loading .env file
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file) as f:
                lines = f.readlines()
            print(f"✅ Found .env file with {len(lines)} lines")
            
            # Check for key variables
            key_vars = ["OPENAI_API_KEY", "ANTHROPIC_API_KEY", "FULL_NAME"]
            found_vars = []
            
            for line in lines:
                if "=" in line and not line.strip().startswith("#"):
                    key = line.split("=")[0].strip()
                    if key in key_vars:
                        found_vars.append(key)
            
            print(f"✅ Found key variables: {', '.join(found_vars)}")
            return True
        else:
            print("⚠️ .env file not found")
            return False
            
    except Exception as e:
        print(f"❌ Environment loading failed: {e}")
        return False

def test_directory_creation():
    """Test directory creation."""
    print("📁 Testing directory creation...")
    
    try:
        # Create test directories
        test_dirs = ["logs", "screenshots", "cache"]
        
        for dir_name in test_dirs:
            Path(dir_name).mkdir(exist_ok=True)
            if Path(dir_name).exists():
                print(f"✅ Created/verified directory: {dir_name}")
            else:
                print(f"❌ Failed to create directory: {dir_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Directory creation failed: {e}")
        return False

def test_sqlite_database():
    """Test SQLite database creation."""
    print("💾 Testing SQLite database...")
    
    try:
        import sqlite3
        
        # Create a test database
        db_path = "test_job_applications.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create a simple test table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Insert test data
        cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("test_entry",))
        conn.commit()
        
        # Query test data
        cursor.execute("SELECT * FROM test_table")
        results = cursor.fetchall()
        
        conn.close()
        
        if results:
            print(f"✅ SQLite database working - found {len(results)} test records")
            # Clean up
            Path(db_path).unlink(missing_ok=True)
            return True
        else:
            print("❌ SQLite database test failed - no data found")
            return False
            
    except Exception as e:
        print(f"❌ SQLite database test failed: {e}")
        return False

def test_json_processing():
    """Test JSON processing for configuration."""
    print("📄 Testing JSON processing...")
    
    try:
        import json
        
        # Test configuration data
        test_config = {
            "user_profile": {
                "name": "Hemanth Kiran Reddy Polu",
                "email": "<EMAIL>",
                "target_roles": ["Software Engineer", "Python Developer"],
                "preferences": {
                    "remote": True,
                    "salary_min": 80000,
                    "salary_max": 150000
                }
            },
            "settings": {
                "max_applications_per_day": 10,
                "headless_mode": False
            }
        }
        
        # Test JSON serialization
        json_str = json.dumps(test_config, indent=2)
        
        # Test JSON deserialization
        parsed_config = json.loads(json_str)
        
        if parsed_config["user_profile"]["name"] == "Hemanth Kiran Reddy Polu":
            print("✅ JSON processing working correctly")
            return True
        else:
            print("❌ JSON processing failed - data mismatch")
            return False
            
    except Exception as e:
        print(f"❌ JSON processing test failed: {e}")
        return False

def test_async_functionality():
    """Test basic async functionality."""
    print("⚡ Testing async functionality...")
    
    try:
        import asyncio
        
        async def test_async_function():
            await asyncio.sleep(0.1)
            return "async_test_success"
        
        # Run async function
        result = asyncio.run(test_async_function())
        
        if result == "async_test_success":
            print("✅ Async functionality working")
            return True
        else:
            print("❌ Async functionality failed")
            return False
            
    except Exception as e:
        print(f"❌ Async functionality test failed: {e}")
        return False

def test_file_operations():
    """Test file operations for resume and documents."""
    print("📋 Testing file operations...")
    
    try:
        # Test file creation
        test_file = Path("test_resume.txt")
        test_content = """
        Hemanth Kiran Reddy Polu
        Software Engineer
        
        Skills: Python, JavaScript, React, Django
        Experience: Full-stack development
        """
        
        # Write test file
        with open(test_file, "w") as f:
            f.write(test_content)
        
        # Read test file
        with open(test_file, "r") as f:
            read_content = f.read()
        
        # Verify content
        if "Hemanth Kiran Reddy Polu" in read_content:
            print("✅ File operations working correctly")
            # Clean up
            test_file.unlink(missing_ok=True)
            return True
        else:
            print("❌ File operations failed - content mismatch")
            return False
            
    except Exception as e:
        print(f"❌ File operations test failed: {e}")
        return False

def main():
    """Run all basic tests."""
    print("🚀 Job Auto Applier - Basic System Test")
    print("=" * 50)
    print("Testing core functionality without external dependencies...")
    print()
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Environment Loading", test_environment_loading),
        ("Directory Creation", test_directory_creation),
        ("SQLite Database", test_sqlite_database),
        ("JSON Processing", test_json_processing),
        ("Async Functionality", test_async_functionality),
        ("File Operations", test_file_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print(f"💡 Fix the {test_name.lower()} issue and try again")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed!")
        print("\n📋 Your system is ready for Job Auto Applier!")
        print("\n🔧 Configuration Summary:")
        
        # Show configuration summary
        env_file = Path(".env")
        if env_file.exists():
            print("✅ Environment file (.env) found")
            print("✅ Directories created (logs, screenshots, cache)")
            print("✅ SQLite database functionality verified")
            print("✅ JSON processing working")
            print("✅ Async functionality ready")
            print("✅ File operations working")
            
            print("\n🚀 Next Steps:")
            print("1. Install dependencies: pip install -r requirements.txt")
            print("2. Install browser: playwright install chromium")
            print("3. Run full setup: python setup_hemanth_profile.py")
            print("4. Test the system: python test_system.py")
            print("5. Run dry run: python -m job_auto_applier.cli.main run --dry-run")
        
        return 0
    else:
        print("⚠️ Some basic tests failed. Please fix the issues before proceeding.")
        print("\n💡 Common fixes:")
        print("- Make sure you're in the correct directory")
        print("- Check that .env file exists and is properly formatted")
        print("- Verify Python 3.9+ is installed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
