[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "job-auto-applier"
version = "1.0.0"
description = "Intelligent, persistent job application agent using browser automation"
authors = [
    {name = "Job Auto Applier Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business",
    "Topic :: Internet :: WWW/HTTP :: Browsers",
]
dependencies = [
    "browser-use>=0.2.5",
    "playwright>=1.40.0",
    "langchain-openai>=0.1.0",
    "langchain-anthropic>=0.1.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "notion-client>=2.2.1",
    "requests>=2.31.0",
    "httpx>=0.25.0",
    "cryptography>=41.0.0",
    "python-dotenv>=1.0.0",
    "keyring>=24.0.0",
    "pandas>=2.1.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "schedule>=1.2.0",
    "aiofiles>=23.0.0",
    "click>=8.1.0",
    "rich>=13.0.0",
    "python-dateutil>=2.8.0",
    "pytz>=2023.3",
    "openai>=1.0.0",
    "anthropic>=0.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
]

[project.scripts]
job-applier = "job_auto_applier.cli:main"

[project.urls]
Homepage = "https://github.com/HemanthKiranPolu/JOB_Auto_Appllier"
Repository = "https://github.com/HemanthKiranPolu/JOB_Auto_Appllier"
Issues = "https://github.com/HemanthKiranPolu/JOB_Auto_Appllier/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["job_auto_applier*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
