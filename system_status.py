#!/usr/bin/env python3
"""
System status check for Job Auto Applier.
Verifies all components are working correctly.
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

def check_configuration():
    """Check configuration status."""
    print("🔧 Configuration Status")
    print("-" * 30)
    
    config = {}
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ .env file found")
        
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    config[key.strip()] = value.strip()
        
        # Check key configurations
        key_configs = {
            "Personal Info": ["FULL_NAME", "EMAIL_ADDRESS", "PHONE_NUMBER"],
            "Job Preferences": ["JOB_KEYWORDS", "PREFERRED_LOCATION", "SALARY_MIN", "SALARY_MAX"],
            "Platform Accounts": ["LINKEDIN_EMAIL", "INDEED_EMAIL", "GLASSDOOR_EMAIL"],
            "AI Providers": ["OPENAI_API_KEY", "ANTHROPIC_API_KEY"],
            "Settings": ["MAX_APPLICATIONS_PER_DAY", "HEADLESS_MODE"]
        }
        
        for category, keys in key_configs.items():
            print(f"\n📋 {category}:")
            for key in keys:
                if key in config and config[key] and config[key] != "your_" + key.lower() + "_here":
                    if "API_KEY" in key or "PASSWORD" in key:
                        print(f"   ✅ {key}: [CONFIGURED]")
                    else:
                        value = config[key][:50] + "..." if len(config[key]) > 50 else config[key]
                        print(f"   ✅ {key}: {value}")
                else:
                    print(f"   ⚠️ {key}: Not configured")
        
        return config
    else:
        print("❌ .env file not found")
        return {}

def check_directories():
    """Check directory structure."""
    print("\n📁 Directory Structure")
    print("-" * 30)
    
    required_dirs = ["logs", "screenshots", "cache"]
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            file_count = len(list(dir_path.glob("*")))
            print(f"✅ {dir_name}/ - {file_count} files")
        else:
            print(f"❌ {dir_name}/ - Missing")

def check_files():
    """Check important files."""
    print("\n📄 Important Files")
    print("-" * 30)
    
    important_files = {
        ".env": "Configuration file",
        "requirements.txt": "Dependencies list",
        "setup_hemanth_profile.py": "Profile setup script",
        "dry_run_test.py": "Dry run test script",
        "simple_test.py": "Basic system test",
        "GET_STARTED.md": "Getting started guide",
        "HEMANTH_SETUP.md": "Personal setup guide"
    }
    
    for file_name, description in important_files.items():
        file_path = Path(file_name)
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {file_name} - {description} ({size:,} bytes)")
        else:
            print(f"❌ {file_name} - {description} (Missing)")

def check_dry_run_results():
    """Check dry run results."""
    print("\n📊 Dry Run Results")
    print("-" * 30)
    
    results_file = Path("dry_run_results.json")
    if results_file.exists():
        try:
            with open(results_file) as f:
                data = json.load(f)
            
            print(f"✅ Last dry run: {data.get('session_date', 'Unknown')}")
            print(f"   Jobs found: {data.get('total_jobs_found', 0)}")
            print(f"   Applications: {data.get('applications_submitted', 0)} submitted, {data.get('redirected_applications', 0)} redirected")
            print(f"   Platforms: {', '.join(data.get('platforms_used', []))}")
            
            if data.get('applications'):
                print(f"   Sample applications:")
                for app in data['applications'][:3]:
                    status_icon = "✅" if app['status'] == 'submitted' else "🔗"
                    print(f"     {status_icon} {app['job_title']} at {app['company']}")
        
        except Exception as e:
            print(f"⚠️ Error reading dry run results: {e}")
    else:
        print("⚠️ No dry run results found - run dry_run_test.py first")

def check_system_readiness():
    """Check overall system readiness."""
    print("\n🚀 System Readiness")
    print("-" * 30)
    
    checks = []
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 9):
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        checks.append(True)
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor}.{python_version.micro} (3.9+ required)")
        checks.append(False)
    
    # Check .env file
    if Path(".env").exists():
        print("✅ Configuration file present")
        checks.append(True)
    else:
        print("❌ Configuration file missing")
        checks.append(False)
    
    # Check directories
    required_dirs = ["logs", "screenshots", "cache"]
    dirs_exist = all(Path(d).exists() for d in required_dirs)
    if dirs_exist:
        print("✅ Required directories created")
        checks.append(True)
    else:
        print("❌ Some directories missing")
        checks.append(False)
    
    # Check dry run
    if Path("dry_run_results.json").exists():
        print("✅ Dry run completed successfully")
        checks.append(True)
    else:
        print("⚠️ Dry run not completed")
        checks.append(False)
    
    # Overall status
    ready_count = sum(checks)
    total_checks = len(checks)
    
    print(f"\n📊 Readiness Score: {ready_count}/{total_checks}")
    
    if ready_count == total_checks:
        print("🎉 System is fully ready for job automation!")
        return True
    elif ready_count >= total_checks - 1:
        print("⚠️ System is mostly ready - minor issues to fix")
        return True
    else:
        print("❌ System needs setup - run installation steps")
        return False

def show_next_steps(config, ready):
    """Show next steps based on system status."""
    print("\n🎯 Next Steps")
    print("-" * 30)
    
    if ready:
        print("Your Job Auto Applier is ready! Here's what you can do:")
        print()
        print("🔍 Test the system:")
        print("   python3 dry_run_test.py")
        print()
        print("🚀 Start job hunting:")
        print("   # Install full dependencies first:")
        print("   pip3 install --user playwright openai anthropic")
        print("   playwright install chromium")
        print("   # Then run the automation:")
        print("   python3 -m job_auto_applier.cli.main run --dry-run")
        print("   python3 -m job_auto_applier.cli.main run")
        print()
        print("📊 Monitor progress:")
        print("   python3 -m job_auto_applier.cli.main status")
        print()
        print("⚙️ Your current settings:")
        if config:
            print(f"   • Target roles: {config.get('JOB_KEYWORDS', 'Not set')}")
            print(f"   • Location: {config.get('PREFERRED_LOCATION', 'Not set')}")
            print(f"   • Salary range: ${config.get('SALARY_MIN', '0')} - ${config.get('SALARY_MAX', '0')}")
            print(f"   • Max applications/day: {config.get('MAX_APPLICATIONS_PER_DAY', '10')}")
    else:
        print("Complete these steps to get ready:")
        print()
        print("1. 📋 Run basic setup:")
        print("   python3 simple_test.py")
        print()
        print("2. 🧪 Test the system:")
        print("   python3 dry_run_test.py")
        print()
        print("3. 📦 Install dependencies:")
        print("   pip3 install --user -r requirements.txt")
        print("   playwright install chromium")
        print()
        print("4. 🚀 Start automation:")
        print("   python3 -m job_auto_applier.cli.main run")

def main():
    """Main status check function."""
    print("🔍 Job Auto Applier - System Status Check")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all checks
    config = check_configuration()
    check_directories()
    check_files()
    check_dry_run_results()
    ready = check_system_readiness()
    
    # Show next steps
    show_next_steps(config, ready)
    
    print("\n" + "=" * 60)
    if ready:
        print("🎉 Status: READY FOR JOB AUTOMATION")
    else:
        print("⚠️ Status: SETUP REQUIRED")
    
    print("\nFor detailed help, see:")
    print("• GET_STARTED.md - Quick start guide")
    print("• HEMANTH_SETUP.md - Your personalized setup")
    print("• README.md - Complete documentation")
    
    return 0 if ready else 1

if __name__ == "__main__":
    sys.exit(main())
