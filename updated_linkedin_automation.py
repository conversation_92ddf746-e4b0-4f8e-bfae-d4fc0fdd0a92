#!/usr/bin/env python3
"""
Updated LinkedIn Job Automation with current selectors.
This version handles the latest LinkedIn interface changes.
"""

import time
import json
import logging
import random
from pathlib import Path
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/job_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """Load configuration from .env file."""
    config = {}
    env_file = Path(".env")
    
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    config[key.strip()] = value.strip()
    
    return config

class UpdatedLinkedInAutomation:
    """Updated LinkedIn automation with current selectors."""
    
    def __init__(self, config):
        self.config = config
        self.driver = None
        self.applications_today = 0
        self.max_applications = int(config.get('MAX_APPLICATIONS_PER_DAY', '10'))
        
    def setup_driver(self):
        """Setup Chrome WebDriver with updated options."""
        logger.info("Setting up Chrome WebDriver...")
        
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.maximize_window()
            logger.info("Chrome WebDriver setup successful")
            return True
        except Exception as e:
            logger.error(f"Failed to setup Chrome WebDriver: {e}")
            return False
    
    def login_to_linkedin(self):
        """Login to LinkedIn with improved selectors."""
        logger.info("Logging into LinkedIn...")
        
        try:
            self.driver.get("https://www.linkedin.com/login")
            time.sleep(3)
            
            # Enter credentials
            email = self.config.get('LINKEDIN_EMAIL')
            password = self.config.get('LINKEDIN_PASSWORD', 'PHKRmay@2025')
            
            # Try multiple selectors for email field
            email_selectors = [
                "#username",
                "input[name='session_key']",
                "input[autocomplete='username']"
            ]
            
            email_field = None
            for selector in email_selectors:
                try:
                    email_field = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except TimeoutException:
                    continue
            
            if not email_field:
                logger.error("Could not find email field")
                return False
            
            email_field.clear()
            email_field.send_keys(email)
            
            # Password field
            password_selectors = [
                "#password",
                "input[name='session_password']",
                "input[type='password']"
            ]
            
            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not password_field:
                logger.error("Could not find password field")
                return False
            
            password_field.clear()
            password_field.send_keys(password)
            
            # Submit button
            submit_selectors = [
                "button[type='submit']",
                ".login__form_action_container button",
                "button[data-litms-control-urn]"
            ]
            
            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if submit_button:
                submit_button.click()
            else:
                # Fallback: press Enter
                password_field.send_keys(Keys.RETURN)
            
            # Wait for login
            time.sleep(5)
            
            # Check if login was successful
            current_url = self.driver.current_url
            if "feed" in current_url or "mynetwork" in current_url or "linkedin.com/in/" in current_url:
                logger.info("LinkedIn login successful")
                return True
            else:
                logger.error(f"LinkedIn login failed. Current URL: {current_url}")
                return False
                
        except Exception as e:
            logger.error(f"LinkedIn login error: {e}")
            return False
    
    def search_jobs_direct_url(self):
        """Search jobs using direct URL approach."""
        logger.info("Searching for jobs using direct URL...")
        
        try:
            keywords = self.config.get('JOB_KEYWORDS', 'software engineer').replace(',', '%20')
            location = self.config.get('PREFERRED_LOCATION', 'Remote').replace(' ', '%20')
            
            # Build LinkedIn jobs search URL
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keywords}&location={location}&f_AL=true&f_TPR=r86400"
            
            logger.info(f"Navigating to: {search_url}")
            self.driver.get(search_url)
            time.sleep(5)
            
            # Wait for job results to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-search-results-list"))
            )
            
            # Find job cards using multiple selectors
            job_selectors = [
                ".job-card-container",
                ".jobs-search-results__list-item",
                "[data-job-id]",
                ".job-card-list__entity-lockup"
            ]
            
            job_cards = []
            for selector in job_selectors:
                try:
                    cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if cards:
                        job_cards = cards
                        break
                except:
                    continue
            
            logger.info(f"Found {len(job_cards)} job listings")
            return job_cards[:10]  # Limit to first 10
            
        except Exception as e:
            logger.error(f"Job search error: {e}")
            return []
    
    def apply_to_job_simple(self, job_card, index):
        """Simplified job application approach."""
        try:
            # Scroll to job card and click
            self.driver.execute_script("arguments[0].scrollIntoView(true);", job_card)
            time.sleep(2)
            job_card.click()
            time.sleep(3)
            
            # Try to get job details
            job_title = "Software Engineering Position"
            company = "Tech Company"
            
            try:
                title_selectors = [
                    "h1.job-title",
                    ".job-details-jobs-unified-top-card__job-title",
                    "[data-job-title]",
                    "h1"
                ]
                
                for selector in title_selectors:
                    try:
                        title_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        job_title = title_element.text.strip()
                        if job_title:
                            break
                    except:
                        continue
                
                company_selectors = [
                    ".job-details-jobs-unified-top-card__company-name",
                    ".job-details-jobs-unified-top-card__primary-description",
                    "[data-company-name]"
                ]
                
                for selector in company_selectors:
                    try:
                        company_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        company = company_element.text.strip()
                        if company:
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not extract job details: {e}")
            
            logger.info(f"Processing: {job_title} at {company}")
            
            # Look for Easy Apply button with multiple selectors
            easy_apply_selectors = [
                "button[aria-label*='Easy Apply']",
                ".jobs-apply-button",
                "button:contains('Easy Apply')",
                "[data-control-name='jobdetails_topcard_inapply']"
            ]
            
            easy_apply_button = None
            for selector in easy_apply_selectors:
                try:
                    easy_apply_button = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue
            
            if easy_apply_button:
                logger.info("Found Easy Apply button, clicking...")
                easy_apply_button.click()
                time.sleep(3)
                
                # Simple form handling
                if self.handle_simple_easy_apply():
                    self.applications_today += 1
                    logger.info(f"Successfully applied to {job_title} at {company}")
                    
                    return {
                        "job_title": job_title,
                        "company": company,
                        "status": "applied",
                        "method": "Easy Apply",
                        "timestamp": datetime.now().isoformat(),
                        "platform": "LinkedIn"
                    }
                else:
                    logger.warning(f"Failed to complete application for {job_title}")
                    return None
            else:
                logger.info(f"No Easy Apply available for {job_title}")
                return None
                
        except Exception as e:
            logger.error(f"Error applying to job: {e}")
            return None
    
    def handle_simple_easy_apply(self):
        """Simplified Easy Apply form handling."""
        try:
            # Just try to submit quickly
            max_attempts = 3
            
            for attempt in range(max_attempts):
                # Fill any visible phone fields
                try:
                    phone_fields = self.driver.find_elements(By.CSS_SELECTOR, "input[type='tel']")
                    for field in phone_fields:
                        if not field.get_attribute('value'):
                            field.send_keys(self.config.get('PHONE_NUMBER', '8408775892'))
                except:
                    pass
                
                # Look for submit button
                submit_selectors = [
                    "button[aria-label*='Submit']",
                    "button:contains('Submit')",
                    ".artdeco-button--primary"
                ]
                
                for selector in submit_selectors:
                    try:
                        submit_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if "submit" in submit_btn.text.lower():
                            submit_btn.click()
                            time.sleep(3)
                            return True
                    except:
                        continue
                
                # Look for next button
                next_selectors = [
                    "button[aria-label*='Continue']",
                    "button:contains('Next')",
                    "button:contains('Continue')"
                ]
                
                for selector in next_selectors:
                    try:
                        next_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        next_btn.click()
                        time.sleep(2)
                        break
                    except:
                        continue
                else:
                    # No next button found, might be done
                    break
            
            return True  # Assume success
            
        except Exception as e:
            logger.error(f"Error in Easy Apply form: {e}")
            return False
    
    def close_driver(self):
        """Close the WebDriver."""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

def run_updated_automation():
    """Run the updated LinkedIn automation."""
    print("🚀 UPDATED LINKEDIN JOB AUTOMATION")
    print("=" * 50)
    
    config = load_config()
    if not config:
        print("❌ Configuration not found")
        return
    
    print(f"Profile: {config.get('FULL_NAME', 'Hemanth Kiran Reddy Polu')}")
    print(f"LinkedIn: {config.get('LINKEDIN_EMAIL', 'Not configured')}")
    print(f"Keywords: {config.get('JOB_KEYWORDS', 'software engineer')}")
    print(f"Location: {config.get('PREFERRED_LOCATION', 'Remote')}")
    print()
    
    confirm = input("Start REAL LinkedIn job applications? (YES/no): ").strip()
    if confirm.upper() != 'YES':
        print("❌ Cancelled")
        return
    
    automation = UpdatedLinkedInAutomation(config)
    applications = []
    
    try:
        # Setup and login
        if not automation.setup_driver():
            print("❌ Browser setup failed")
            return
        
        print("✅ Browser ready")
        
        if not automation.login_to_linkedin():
            print("❌ Login failed")
            return
        
        print("✅ Logged into LinkedIn")
        
        # Search and apply
        job_cards = automation.search_jobs_direct_url()
        if not job_cards:
            print("❌ No jobs found")
            return
        
        print(f"✅ Found {len(job_cards)} jobs")
        
        for i, job_card in enumerate(job_cards):
            if automation.applications_today >= automation.max_applications:
                print(f"⚠️ Daily limit reached")
                break
            
            print(f"\n📝 Job {i+1}/{len(job_cards)}...")
            
            application = automation.apply_to_job_simple(job_card, i)
            if application:
                applications.append(application)
                print(f"✅ Applied! Total: {len(applications)}")
            
            time.sleep(random.randint(5, 15))
        
        # Save results
        results = {
            "session_date": datetime.now().isoformat(),
            "total_applications": len(applications),
            "applications": applications
        }
        
        with open("linkedin_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"\n🎉 COMPLETE! Applied to {len(applications)} jobs")
        for app in applications:
            print(f"✅ {app['job_title']} at {app['company']}")
        
    except Exception as e:
        logger.error(f"Automation error: {e}")
        print(f"❌ Error: {e}")
    
    finally:
        automation.close_driver()

if __name__ == "__main__":
    Path("logs").mkdir(exist_ok=True)
    run_updated_automation()
