# Job Auto Applier Usage Guide

This guide provides detailed instructions on how to use the Job Auto Applier system effectively.

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/HemanthKiranPolu/JOB_Auto_Appllier.git
cd JOB_Auto_Appllier

# Run the installation script
./install.sh

# Activate the virtual environment
source venv/bin/activate
```

### 2. Configuration

```bash
# Copy and edit the environment file
cp .env.example .env
# Edit .env with your API keys and preferences
```

Required environment variables:
- `OPENAI_API_KEY` or `ANTHROPIC_API_KEY` - For AI-powered responses
- `NOTION_API_KEY` and `NOTION_DATABASE_ID` - For application tracking

### 3. Setup Your Profile

```bash
# Run the interactive setup
job-applier setup

# Or setup with a specific profile name
job-applier setup --profile software-engineer
```

The setup will guide you through:
- Personal information
- Job preferences and criteria
- Standard application answers
- Resume upload
- Platform credentials (LinkedIn, Indeed, etc.)
- API integrations

### 4. Run Job Applications

```bash
# Run with default settings
job-applier run

# Run with specific options
job-applier run --max-applications 15 --profile software-engineer

# Dry run to see what jobs would be found
job-applier run --dry-run
```

### 5. Check Status

```bash
# Check recent application status
job-applier status

# Detailed status for last 14 days
job-applier status --days 14 --detailed
```

## Detailed Usage

### Profile Management

#### Creating Multiple Profiles

You can create different profiles for different types of positions:

```bash
# Software engineering profile
job-applier setup --profile software-engineer

# DevOps profile
job-applier setup --profile devops-engineer

# Data science profile
job-applier setup --profile data-scientist
```

#### Using Different Profiles

```bash
# Run with specific profile
job-applier run --profile devops-engineer

# Check status for specific profile
job-applier status --profile data-scientist
```

### Job Search Configuration

#### Target Roles
Configure the types of positions you're interested in:
- Software Engineer
- Cloud Engineer
- DevOps Engineer
- Full Stack Developer
- Backend Developer
- Frontend Developer

#### Experience Levels
- Internship
- Entry Level
- Associate
- Mid Level
- Senior

#### Locations
- Remote
- Specific cities (e.g., "San Francisco, CA")
- Multiple locations

#### Keywords and Skills
Required skills that must be mentioned in job descriptions:
- Programming languages (Python, JavaScript, Java)
- Technologies (AWS, Docker, Kubernetes)
- Frameworks (React, Django, Spring)

### Application Automation

#### Easy Apply vs External Applications

**Easy Apply (Recommended)**
- Faster application process
- Higher success rate
- Automated form filling
- Resume upload

**External Company Websites**
- More complex forms
- May require manual intervention
- Higher chance of errors
- Better for targeted applications

#### Daily Limits

Set reasonable daily application limits:
- **Conservative**: 5-10 applications per day
- **Moderate**: 10-15 applications per day
- **Aggressive**: 15-25 applications per day

### AI-Powered Responses

The system uses AI to answer novel application questions by:

1. **Categorizing questions** into types (motivation, experience, technical, etc.)
2. **Using pre-configured answers** for common questions
3. **Generating custom responses** for unique questions
4. **Contextualizing answers** based on the specific job and company

#### Improving AI Responses

To get better AI responses:
- Provide detailed answers during setup
- Include specific examples and achievements
- Mention relevant technologies and projects
- Keep answers concise but informative

### Notion Integration

#### Setting Up Notion Database

1. Create a new Notion database
2. Add the following properties:
   - Job Title (Title)
   - Company (Text)
   - Status (Select)
   - Application Date (Date)
   - Job URL (URL)
   - Method (Select)
   - Days Since Applied (Number)
   - Interviews (Number)
   - Automated (Checkbox)
   - Needs Follow-up (Checkbox)

3. Get your Notion API key and database ID
4. Add them to your .env file

#### Tracking Features

The Notion integration provides:
- **Automatic page creation** for each application
- **Status updates** as applications progress
- **Communication logging** for responses and interviews
- **Statistics and analytics** on application performance
- **Follow-up reminders** for pending applications

### Security and Privacy

#### Data Encryption

All sensitive data is encrypted:
- **Platform credentials** stored in system keyring
- **Personal information** encrypted at rest
- **Application data** secured in local database

#### Privacy Features

- **No data sharing** - everything stays on your system
- **Secure credential storage** using system keyring
- **Encrypted database** for application tracking
- **Optional cloud sync** through Notion (encrypted)

### Troubleshooting

#### Common Issues

**Browser Automation Fails**
```bash
# Reinstall browser dependencies
playwright install chromium --with-deps

# Check if running in headless mode
# Set HEADLESS_MODE=false in .env for debugging
```

**Login Issues**
```bash
# Update credentials
job-applier setup --force

# Check for CAPTCHA or 2FA requirements
# May need manual intervention
```

**API Errors**
```bash
# Check API keys in .env file
# Verify Notion database permissions
# Check rate limits
```

**Application Errors**
```bash
# Review automation errors in status
job-applier status --detailed

# Check logs for detailed error information
tail -f logs/job_applier.log
```

#### Debug Mode

```bash
# Run with debug logging
job-applier --debug run

# Test system components
job-applier test
```

### Best Practices

#### Application Strategy

1. **Start with Easy Apply** jobs for higher success rates
2. **Set realistic daily limits** to avoid being flagged
3. **Customize answers** for different types of roles
4. **Monitor application status** regularly
5. **Follow up** on pending applications after 1-2 weeks

#### Profile Optimization

1. **Keep resume updated** and tailored for your target roles
2. **Use relevant keywords** in your application answers
3. **Provide specific examples** of your experience
4. **Update job preferences** based on market feedback

#### Automation Hygiene

1. **Run daily** for best results
2. **Review errors** and adjust settings
3. **Update credentials** if login issues occur
4. **Monitor for CAPTCHA** and anti-bot measures
5. **Respect platform terms of service**

### Advanced Features

#### Custom Question Responses

Add custom responses for specific questions:

```python
# In your application answers
custom_answers = {
    "Why do you want to work at our company?": "I'm excited about your company's mission...",
    "What's your salary expectation?": "I'm open to discussing compensation based on the role...",
}
```

#### Scheduling Automation

Set up daily automation using cron:

```bash
# Add to crontab (crontab -e)
0 9 * * * /path/to/venv/bin/job-applier run --profile default
```

#### Integration with Other Tools

- **Slack notifications** for application updates
- **Email alerts** for important status changes
- **Calendar integration** for interview scheduling
- **CRM integration** for lead tracking

### Support and Community

#### Getting Help

1. **Check the logs** for detailed error information
2. **Review the documentation** and usage guide
3. **Search existing issues** on GitHub
4. **Create a new issue** with detailed information

#### Contributing

1. **Report bugs** and suggest improvements
2. **Submit pull requests** for new features
3. **Share your success stories** and tips
4. **Help other users** in discussions

#### Staying Updated

1. **Watch the repository** for updates
2. **Follow release notes** for new features
3. **Update regularly** to get the latest improvements
4. **Backup your data** before major updates

---

For more information, see the [README.md](README.md) file or visit the [GitHub repository](https://github.com/HemanthKiranPolu/JOB_Auto_Appllier).
