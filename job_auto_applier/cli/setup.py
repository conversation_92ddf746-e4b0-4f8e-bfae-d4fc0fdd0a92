"""Setup command for user profile configuration."""

import asyncio
import getpass
import logging
from pathlib import Path
from typing import List, Optional

import click
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

from ..config.settings import get_settings
from ..config.encryption import EncryptionManager
from ..models.user_profile import (
    UserProfile, PersonalInfo, JobPreferences, ApplicationAnswers,
    ExperienceLevel, CompanySize, WorkType
)
from ..models.database import get_database_manager

console = Console()
logger = logging.getLogger(__name__)


async def setup_command(profile_name: str, force: bool) -> None:
    """Setup user profile and configuration."""
    console.print(f"\n🚀 Setting up Job Auto Applier profile: [bold blue]{profile_name}[/bold blue]")
    
    try:
        # Check if profile already exists
        db_manager = get_database_manager()
        existing_profile = db_manager.get_user_profile(profile_name)
        
        if existing_profile and not force:
            console.print(f"Profile '{profile_name}' already exists. Use --force to overwrite.", style="yellow")
            return
        
        # Start setup process
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # Step 1: Personal Information
            task = progress.add_task("Collecting personal information...", total=None)
            personal_info = await collect_personal_info()
            progress.update(task, description="✓ Personal information collected")
            
            # Step 2: Job Preferences
            task = progress.add_task("Setting up job preferences...", total=None)
            job_preferences = await collect_job_preferences()
            progress.update(task, description="✓ Job preferences configured")
            
            # Step 3: Application Answers
            task = progress.add_task("Preparing application answers...", total=None)
            application_answers = await collect_application_answers()
            progress.update(task, description="✓ Application answers prepared")
            
            # Step 4: Resume Upload
            task = progress.add_task("Setting up resume...", total=None)
            resume_path = await setup_resume()
            progress.update(task, description="✓ Resume configured")
            
            # Step 5: Credentials
            task = progress.add_task("Securing credentials...", total=None)
            credentials = await collect_credentials()
            progress.update(task, description="✓ Credentials secured")
            
            # Step 6: API Integrations
            task = progress.add_task("Testing integrations...", total=None)
            await test_integrations()
            progress.update(task, description="✓ Integrations tested")
        
        # Create user profile
        user_profile = UserProfile(
            user_id=profile_name,
            personal_info=personal_info,
            job_preferences=job_preferences,
            application_answers=application_answers,
            resume_path=resume_path,
            **credentials
        )
        
        # Save profile
        db_manager.save_user_profile(user_profile)
        
        # Success message
        console.print(Panel(
            f"✅ Profile '[bold green]{profile_name}[/bold green]' setup completed successfully!\n\n"
            f"You can now run: [bold blue]job-applier run --profile {profile_name}[/bold blue]",
            title="Setup Complete",
            border_style="green"
        ))
        
    except KeyboardInterrupt:
        console.print("\nSetup cancelled by user", style="yellow")
    except Exception as e:
        console.print(f"Setup failed: {e}", style="red")
        logger.error(f"Setup error: {e}", exc_info=True)


async def collect_personal_info() -> PersonalInfo:
    """Collect personal information from user."""
    console.print("\n📋 Personal Information", style="bold blue")
    console.print("Please provide your personal details for job applications:")
    
    first_name = Prompt.ask("First name")
    last_name = Prompt.ask("Last name")
    email = Prompt.ask("Email address")
    phone = Prompt.ask("Phone number", default="")
    location = Prompt.ask("Current location (e.g., 'San Francisco, CA')")
    
    # Optional URLs
    linkedin_url = Prompt.ask("LinkedIn profile URL", default="")
    github_url = Prompt.ask("GitHub profile URL", default="")
    portfolio_url = Prompt.ask("Portfolio website URL", default="")
    
    return PersonalInfo(
        first_name=first_name,
        last_name=last_name,
        email=email,
        phone=phone if phone else None,
        location=location,
        linkedin_url=linkedin_url if linkedin_url else None,
        github_url=github_url if github_url else None,
        portfolio_url=portfolio_url if portfolio_url else None,
    )


async def collect_job_preferences() -> JobPreferences:
    """Collect job search preferences."""
    console.print("\n🎯 Job Search Preferences", style="bold blue")
    console.print("Configure your job search criteria:")
    
    # Target roles
    console.print("\nTarget roles (comma-separated):")
    console.print("Examples: Software Engineer, Cloud Engineer, DevOps Engineer")
    roles_input = Prompt.ask("Enter target roles")
    target_roles = [role.strip() for role in roles_input.split(",")]
    
    # Experience levels
    console.print("\nExperience levels:")
    for i, level in enumerate(ExperienceLevel, 1):
        console.print(f"{i}. {level.value.replace('_', ' ').title()}")
    
    levels_input = Prompt.ask("Select experience levels (comma-separated numbers)", default="1,2")
    selected_levels = []
    for num in levels_input.split(","):
        try:
            idx = int(num.strip()) - 1
            if 0 <= idx < len(ExperienceLevel):
                selected_levels.append(list(ExperienceLevel)[idx])
        except ValueError:
            pass
    
    if not selected_levels:
        selected_levels = [ExperienceLevel.ENTRY_LEVEL]
    
    # Locations
    console.print("\nPreferred locations (comma-separated):")
    console.print("Examples: Remote, San Francisco CA, New York NY")
    locations_input = Prompt.ask("Enter preferred locations")
    preferred_locations = [loc.strip() for loc in locations_input.split(",")]
    
    # Work types
    work_types = [WorkType.REMOTE]  # Default to remote
    if Confirm.ask("Include hybrid positions?"):
        work_types.append(WorkType.HYBRID)
    if Confirm.ask("Include on-site positions?"):
        work_types.append(WorkType.ON_SITE)
    
    # Salary preferences
    min_salary = None
    if Confirm.ask("Set minimum salary requirement?"):
        min_salary = int(Prompt.ask("Minimum salary (USD)"))
    
    # Keywords
    console.print("\nRequired skills/keywords (comma-separated):")
    console.print("Examples: Python, AWS, Kubernetes, React")
    keywords_input = Prompt.ask("Enter required keywords", default="")
    required_keywords = [kw.strip() for kw in keywords_input.split(",") if kw.strip()]
    
    # Application settings
    max_applications = int(Prompt.ask("Maximum applications per day", default="10"))
    auto_apply_easy = Confirm.ask("Auto-apply to Easy Apply jobs?", default=True)
    auto_apply_external = Confirm.ask("Auto-apply to external company sites?", default=False)
    
    return JobPreferences(
        target_roles=target_roles,
        experience_levels=selected_levels,
        preferred_locations=preferred_locations,
        work_types=work_types,
        min_salary=min_salary,
        required_keywords=required_keywords,
        max_applications_per_day=max_applications,
        auto_apply_easy_apply=auto_apply_easy,
        auto_apply_external=auto_apply_external,
    )


async def collect_application_answers() -> ApplicationAnswers:
    """Collect standard application answers."""
    console.print("\n💬 Application Answers", style="bold blue")
    console.print("Prepare answers for common application questions:")
    
    console.print("\n1. Why are you interested in this type of role?")
    why_interested = Prompt.ask("Your answer (2-3 sentences)")
    
    console.print("\n2. What makes you a good fit for these positions?")
    why_good_fit = Prompt.ask("Your answer (2-3 sentences)")
    
    console.print("\n3. What are your career goals?")
    career_goals = Prompt.ask("Your answer (2-3 sentences)")
    
    console.print("\n4. Describe your relevant experience:")
    relevant_experience = Prompt.ask("Your answer (2-3 sentences)")
    
    console.print("\n5. What's your biggest professional achievement?")
    biggest_achievement = Prompt.ask("Your answer (2-3 sentences)")
    
    console.print("\n6. Describe your technical skills:")
    technical_skills = Prompt.ask("Your answer (2-3 sentences)")
    
    # Logistics
    available_start = Prompt.ask("Available start date", default="2 weeks notice")
    willing_relocate = Confirm.ask("Willing to relocate?", default=False)
    requires_sponsorship = Confirm.ask("Require work authorization/sponsorship?", default=False)
    
    return ApplicationAnswers(
        why_interested=why_interested,
        why_good_fit=why_good_fit,
        career_goals=career_goals,
        relevant_experience=relevant_experience,
        biggest_achievement=biggest_achievement,
        technical_skills=technical_skills,
        available_start_date=available_start,
        willing_to_relocate=willing_relocate,
        requires_sponsorship=requires_sponsorship,
    )


async def setup_resume() -> Optional[Path]:
    """Setup resume file."""
    console.print("\n📄 Resume Setup", style="bold blue")
    
    if not Confirm.ask("Do you want to upload a resume file?"):
        return None
    
    while True:
        resume_path_str = Prompt.ask("Enter path to your resume file (PDF recommended)")
        resume_path = Path(resume_path_str).expanduser()
        
        if resume_path.exists():
            # Copy to data directory
            settings = get_settings()
            resume_dir = settings.data_dir / "resumes"
            resume_dir.mkdir(exist_ok=True)
            
            new_path = resume_dir / f"resume_{resume_path.suffix}"
            import shutil
            shutil.copy2(resume_path, new_path)
            
            console.print(f"✓ Resume copied to {new_path}", style="green")
            return new_path
        else:
            console.print("File not found. Please try again.", style="red")


async def collect_credentials() -> dict:
    """Collect and encrypt platform credentials."""
    console.print("\n🔐 Platform Credentials", style="bold blue")
    console.print("Securely store your job platform login credentials:")
    
    encryption_manager = EncryptionManager()
    credentials = {}
    
    # LinkedIn credentials
    if Confirm.ask("Add LinkedIn credentials?", default=True):
        linkedin_email = Prompt.ask("LinkedIn email")
        linkedin_password = getpass.getpass("LinkedIn password: ")
        
        # Store encrypted
        encryption_manager.store_credential(linkedin_email, "linkedin", linkedin_password)
        credentials["linkedin_email"] = linkedin_email
        console.print("✓ LinkedIn credentials stored securely", style="green")
    
    # Indeed credentials
    if Confirm.ask("Add Indeed credentials?", default=False):
        indeed_email = Prompt.ask("Indeed email")
        indeed_password = getpass.getpass("Indeed password: ")
        
        encryption_manager.store_credential(indeed_email, "indeed", indeed_password)
        credentials["indeed_email"] = indeed_email
        console.print("✓ Indeed credentials stored securely", style="green")
    
    # Glassdoor credentials
    if Confirm.ask("Add Glassdoor credentials?", default=False):
        glassdoor_email = Prompt.ask("Glassdoor email")
        glassdoor_password = getpass.getpass("Glassdoor password: ")
        
        encryption_manager.store_credential(glassdoor_email, "glassdoor", glassdoor_password)
        credentials["glassdoor_email"] = glassdoor_email
        console.print("✓ Glassdoor credentials stored securely", style="green")
    
    return credentials


async def test_integrations() -> None:
    """Test API integrations."""
    console.print("\n🧪 Testing Integrations", style="bold blue")
    
    settings = get_settings()
    
    # Test AI provider
    try:
        provider = settings.get_ai_provider()
        console.print(f"✓ AI provider configured: {provider}", style="green")
    except Exception as e:
        console.print(f"⚠ AI provider issue: {e}", style="yellow")
    
    # Test Notion integration
    if settings.has_notion_integration():
        try:
            from ..tracking.notion_tracker import NotionTracker
            tracker = NotionTracker()
            await tracker.initialize_database()
            console.print("✓ Notion integration working", style="green")
        except Exception as e:
            console.print(f"⚠ Notion integration issue: {e}", style="yellow")
    else:
        console.print("ℹ Notion integration not configured", style="blue")
    
    # Test browser automation
    try:
        import playwright
        console.print("✓ Browser automation ready", style="green")
    except ImportError:
        console.print("⚠ Browser automation not available - run 'playwright install'", style="yellow")
