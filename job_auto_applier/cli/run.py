"""Run command for executing job application automation."""

import asyncio
import logging
from datetime import datetime
from typing import List, Optional

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.live import Live

from ..core.agent import JobApplicationAgent
from ..models.database import get_database_manager
from ..config.settings import get_settings

console = Console()
logger = logging.getLogger(__name__)


async def run_command(profile_name: str, max_applications: Optional[int], 
                     dry_run: bool, platforms: List[str]) -> None:
    """Run the job application automation."""
    console.print(f"\n🚀 Starting Job Auto Applier for profile: [bold blue]{profile_name}[/bold blue]")
    
    if dry_run:
        console.print("🧪 [yellow]DRY RUN MODE[/yellow] - No actual applications will be submitted")
    
    try:
        # Load user profile
        db_manager = get_database_manager()
        user_profile = db_manager.get_user_profile(profile_name)
        
        if not user_profile:
            console.print(f"❌ Profile '{profile_name}' not found. Run setup first.", style="red")
            return
        
        # Override max applications if specified
        if max_applications:
            user_profile.job_preferences.max_applications_per_day = max_applications
        
        # Initialize agent
        agent = JobApplicationAgent(user_profile)
        
        if not await agent.initialize():
            console.print("❌ Failed to initialize agent", style="red")
            return
        
        # Display run configuration
        display_run_config(user_profile, dry_run, platforms)
        
        # Run the automation
        if dry_run:
            results = await run_dry_run(agent)
        else:
            results = await run_live_automation(agent)
        
        # Display results
        display_results(results)
        
        # Cleanup
        await agent.cleanup()
        
    except KeyboardInterrupt:
        console.print("\n⏹️ Automation stopped by user", style="yellow")
    except Exception as e:
        console.print(f"❌ Automation failed: {e}", style="red")
        logger.error(f"Run command error: {e}", exc_info=True)


def display_run_config(user_profile, dry_run: bool, platforms: List[str]) -> None:
    """Display the run configuration."""
    config_table = Table(title="Run Configuration")
    config_table.add_column("Setting", style="cyan")
    config_table.add_column("Value", style="white")
    
    config_table.add_row("Profile", user_profile.user_id)
    config_table.add_row("Target Roles", ", ".join(user_profile.job_preferences.target_roles))
    config_table.add_row("Locations", ", ".join(user_profile.job_preferences.preferred_locations))
    config_table.add_row("Max Applications", str(user_profile.job_preferences.max_applications_per_day))
    config_table.add_row("Mode", "Dry Run" if dry_run else "Live")
    config_table.add_row("Platforms", ", ".join(platforms) if platforms else "All configured")
    
    console.print(config_table)
    console.print()


async def run_dry_run(agent: JobApplicationAgent) -> dict:
    """Run in dry-run mode (search only, no applications)."""
    console.print("🔍 Searching for jobs (dry run)...\n")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        
        # Search for jobs
        search_task = progress.add_task("Searching for jobs...", total=None)
        
        # Simulate job search
        await asyncio.sleep(2)  # Simulate search time
        
        progress.update(search_task, description="✓ Job search completed")
        
        # Simulate filtering
        filter_task = progress.add_task("Filtering and ranking jobs...", total=None)
        await asyncio.sleep(1)
        progress.update(filter_task, description="✓ Jobs filtered and ranked")
    
    # Return mock results for dry run
    return {
        "jobs_found": 15,
        "applications_submitted": 0,
        "errors": [],
        "summary": {
            "dry_run": True,
            "would_apply_to": 8,
            "top_matches": [
                {"title": "Software Engineer", "company": "Tech Corp", "match_score": 0.85},
                {"title": "Cloud Engineer", "company": "Cloud Inc", "match_score": 0.78},
                {"title": "DevOps Engineer", "company": "Startup XYZ", "match_score": 0.72},
            ]
        }
    }


async def run_live_automation(agent: JobApplicationAgent) -> dict:
    """Run live automation with real applications."""
    console.print("🎯 Running live job application automation...\n")
    
    # Create a live display for real-time updates
    status_table = Table(title="Automation Status")
    status_table.add_column("Stage", style="cyan")
    status_table.add_column("Status", style="white")
    status_table.add_column("Details", style="dim")
    
    with Live(status_table, refresh_per_second=2, console=console) as live:
        
        # Update status function
        def update_status(stage: str, status: str, details: str = ""):
            status_table.rows.clear()
            status_table.add_row("🔍 Job Search", "✓ Complete" if stage != "search" else "🔄 Running", "")
            status_table.add_row("📋 Filtering", "✓ Complete" if stage in ["apply", "complete"] else ("🔄 Running" if stage == "filter" else "⏳ Pending"), "")
            status_table.add_row("📤 Applications", "✓ Complete" if stage == "complete" else ("🔄 Running" if stage == "apply" else "⏳ Pending"), details)
            live.update(status_table)
        
        # Run the actual automation
        update_status("search", "running")
        results = await agent.run_daily_job_search()
        update_status("complete", "complete", f"{results['applications_submitted']} submitted")
        
        # Keep the final status visible for a moment
        await asyncio.sleep(2)
    
    return results


def display_results(results: dict) -> None:
    """Display automation results."""
    console.print("\n📊 Automation Results", style="bold blue")
    
    # Summary panel
    if results.get("summary", {}).get("dry_run"):
        summary_text = f"""
        🔍 Jobs Found: {results['jobs_found']}
        🎯 Would Apply To: {results['summary']['would_apply_to']}
        ⚠️  This was a dry run - no applications were submitted
        """
    else:
        summary_text = f"""
        🔍 Jobs Found: {results['jobs_found']}
        📤 Applications Submitted: {results['applications_submitted']}
        ❌ Errors: {len(results.get('errors', []))}
        """
    
    console.print(Panel(
        summary_text.strip(),
        title="Summary",
        border_style="green" if not results.get('errors') else "yellow"
    ))
    
    # Top matches table
    if results.get("summary", {}).get("top_matches"):
        matches_table = Table(title="Top Job Matches")
        matches_table.add_column("Job Title", style="cyan")
        matches_table.add_column("Company", style="white")
        matches_table.add_column("Match Score", style="green")
        
        for match in results["summary"]["top_matches"]:
            score = match.get("match_score", 0)
            score_str = f"{score:.0%}" if isinstance(score, float) else str(score)
            matches_table.add_row(
                match["title"],
                match["company"],
                score_str
            )
        
        console.print(matches_table)
    
    # Companies applied to
    if results.get("summary", {}).get("companies_applied"):
        companies = results["summary"]["companies_applied"]
        console.print(f"\n🏢 Applied to companies: {', '.join(companies)}")
    
    # Errors
    if results.get("errors"):
        console.print("\n⚠️ Errors encountered:", style="yellow")
        for error in results["errors"]:
            console.print(f"  • {error}", style="red")
    
    # Next steps
    console.print("\n📋 Next Steps:", style="bold blue")
    if results.get("summary", {}).get("dry_run"):
        console.print("  • Run without --dry-run to submit applications")
        console.print("  • Review and adjust job preferences if needed")
    else:
        console.print("  • Check application status with: job-applier status")
        console.print("  • Monitor responses in your Notion database")
        console.print("  • Schedule daily runs for continuous automation")
    
    # Daily limit warning
    if results.get("summary", {}).get("limit_reached"):
        console.print("\n⚠️ Daily application limit reached", style="yellow")
        console.print("  • Increase limit in preferences or wait until tomorrow")


async def schedule_daily_run(profile_name: str) -> None:
    """Schedule daily automation runs."""
    console.print("⏰ Scheduling daily automation runs...", style="blue")
    
    # This would integrate with system scheduler (cron, Windows Task Scheduler, etc.)
    # For now, just show instructions
    
    console.print(Panel(
        f"""
        To schedule daily runs, add this to your crontab:
        
        # Run job automation daily at 9 AM
        0 9 * * * job-applier run --profile {profile_name}
        
        Or use your system's task scheduler.
        """.strip(),
        title="Scheduling Instructions",
        border_style="blue"
    ))
