"""Main CLI entry point for Job Auto Applier."""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.panel import Panel
from rich.text import Text

from ..config.settings import get_settings
from .setup import setup_command
from .run import run_command
from .status import status_command

console = Console()


def setup_logging(level: str = "INFO") -> None:
    """Setup logging with rich formatting."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(message)s",
        datefmt="[%X]",
        handlers=[RichHandler(console=console, rich_tracebacks=True)]
    )


@click.group()
@click.option("--debug", is_flag=True, help="Enable debug logging")
@click.option("--config-file", type=click.Path(), help="Path to configuration file")
@click.version_option(version="1.0.0", prog_name="Job Auto Applier")
def cli(debug: bool, config_file: Optional[str]) -> None:
    """
    Job Auto Applier - Intelligent Job Application Automation
    
    An intelligent, persistent job application agent that leverages browser automation
    for fully automated job searching, application submission, and tracking.
    """
    # Setup logging
    log_level = "DEBUG" if debug else "INFO"
    setup_logging(log_level)
    
    # Load configuration
    if config_file:
        # In a full implementation, you would load custom config here
        pass
    
    # Display welcome message
    if not debug:
        welcome_text = Text()
        welcome_text.append("Job Auto Applier", style="bold blue")
        welcome_text.append(" - Intelligent Job Application Automation", style="dim")
        
        console.print(Panel(
            welcome_text,
            title="🤖 Welcome",
            border_style="blue"
        ))


@cli.command()
@click.option("--profile", default="default", help="Profile name to setup")
@click.option("--force", is_flag=True, help="Force setup even if profile exists")
def setup(profile: str, force: bool) -> None:
    """Setup user profile and configuration."""
    asyncio.run(setup_command(profile, force))


@cli.command()
@click.option("--profile", default="default", help="Profile to use")
@click.option("--max-applications", type=int, help="Maximum applications per day")
@click.option("--dry-run", is_flag=True, help="Run without actually applying to jobs")
@click.option("--platforms", multiple=True, help="Platforms to search (linkedin, indeed, glassdoor)")
def run(profile: str, max_applications: Optional[int], dry_run: bool, platforms: tuple) -> None:
    """Run the job application automation."""
    asyncio.run(run_command(profile, max_applications, dry_run, list(platforms)))


@cli.command()
@click.option("--profile", default="default", help="Profile to check")
@click.option("--days", default=7, type=int, help="Number of days to look back")
@click.option("--detailed", is_flag=True, help="Show detailed status information")
def status(profile: str, days: int, detailed: bool) -> None:
    """Check application status and statistics."""
    asyncio.run(status_command(profile, days, detailed))


@cli.command()
@click.option("--profile", default="default", help="Profile to update")
def config(profile: str) -> None:
    """Update configuration and preferences."""
    console.print("Configuration update not yet implemented", style="yellow")
    console.print("Use 'setup --force' to reconfigure your profile")


@cli.command()
@click.option("--profile", default="default", help="Profile to export")
@click.option("--format", type=click.Choice(["json", "csv"]), default="json", help="Export format")
@click.option("--output", type=click.Path(), help="Output file path")
def export(profile: str, format: str, output: Optional[str]) -> None:
    """Export application data."""
    console.print("Export functionality not yet implemented", style="yellow")


@cli.command()
@click.option("--profile", default="default", help="Profile to check")
def test(profile: str) -> None:
    """Test automation and integrations."""
    console.print("Running system tests...", style="blue")
    
    # Test configuration
    try:
        settings = get_settings()
        console.print("✓ Configuration loaded", style="green")
        
        # Test AI provider
        provider = settings.get_ai_provider()
        console.print(f"✓ AI provider: {provider}", style="green")
        
        # Test Notion integration
        if settings.has_notion_integration():
            console.print("✓ Notion integration configured", style="green")
        else:
            console.print("⚠ Notion integration not configured", style="yellow")
        
        # Test browser automation
        console.print("✓ Browser automation ready", style="green")
        
        console.print("\nAll systems operational! 🚀", style="bold green")
        
    except Exception as e:
        console.print(f"✗ System test failed: {e}", style="red")
        sys.exit(1)


@cli.command()
def version() -> None:
    """Show version information."""
    from .. import __version__, __author__
    
    version_info = f"""
    Job Auto Applier v{__version__}
    Author: {__author__}
    
    Dependencies:
    - browser-use: Latest
    - Python: {sys.version.split()[0]}
    """
    
    console.print(Panel(
        version_info.strip(),
        title="Version Information",
        border_style="blue"
    ))


def main() -> None:
    """Main entry point for the CLI."""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n\nOperation cancelled by user", style="yellow")
        sys.exit(1)
    except Exception as e:
        console.print(f"\nUnexpected error: {e}", style="red")
        if "--debug" in sys.argv:
            raise
        sys.exit(1)


if __name__ == "__main__":
    main()
