"""Status command for checking application progress."""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

from ..core.agent import JobApplicationAgent
from ..models.database import get_database_manager
from ..tracking.notion_tracker import NotionTracker
from ..config.settings import get_settings

console = Console()
logger = logging.getLogger(__name__)


async def status_command(profile_name: str, days: int, detailed: bool) -> None:
    """Check application status and statistics."""
    console.print(f"\n📊 Application Status for profile: [bold blue]{profile_name}[/bold blue]")
    
    try:
        # Load user profile
        db_manager = get_database_manager()
        user_profile = db_manager.get_user_profile(profile_name)
        
        if not user_profile:
            console.print(f"❌ Profile '{profile_name}' not found.", style="red")
            return
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # Load application data
            task = progress.add_task("Loading application data...", total=None)
            
            # Get applications from database
            applications = db_manager.get_applications_by_user(profile_name)
            
            # Filter by date range
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            recent_apps = [
                app for app in applications 
                if app.application_date >= cutoff_date
            ]
            
            progress.update(task, description="✓ Application data loaded")
            
            # Get Notion statistics if available
            notion_stats = None
            settings = get_settings()
            if settings.has_notion_integration():
                task = progress.add_task("Fetching Notion statistics...", total=None)
                try:
                    notion_tracker = NotionTracker()
                    notion_stats = await notion_tracker.get_application_statistics()
                    progress.update(task, description="✓ Notion statistics loaded")
                except Exception as e:
                    progress.update(task, description="⚠ Notion statistics unavailable")
                    logger.warning(f"Failed to load Notion stats: {e}")
        
        # Display status information
        display_overview(user_profile, recent_apps, days)
        display_application_breakdown(recent_apps)
        
        if detailed:
            display_detailed_applications(recent_apps)
            display_follow_up_needed(recent_apps)
        
        if notion_stats and not notion_stats.get("error"):
            display_notion_statistics(notion_stats)
        
        display_recommendations(recent_apps, user_profile)
        
    except Exception as e:
        console.print(f"❌ Failed to get status: {e}", style="red")
        logger.error(f"Status command error: {e}", exc_info=True)


def display_overview(user_profile, applications: list, days: int) -> None:
    """Display overview statistics."""
    total_apps = len(applications)
    successful_apps = len([app for app in applications if app.status == "submitted"])
    failed_apps = len([app for app in applications if app.status == "failed"])
    
    # Calculate rates
    success_rate = (successful_apps / total_apps * 100) if total_apps > 0 else 0
    
    # Count by status
    status_counts = {}
    for app in applications:
        status = app.status
        status_counts[status] = status_counts.get(status, 0) + 1
    
    overview_text = f"""
    📅 Period: Last {days} days
    📊 Total Applications: {total_apps}
    ✅ Successful: {successful_apps}
    ❌ Failed: {failed_apps}
    📈 Success Rate: {success_rate:.1f}%
    🎯 Daily Limit: {user_profile.job_preferences.max_applications_per_day}
    """
    
    console.print(Panel(
        overview_text.strip(),
        title="Overview",
        border_style="blue"
    ))


def display_application_breakdown(applications: list) -> None:
    """Display application breakdown by status."""
    if not applications:
        console.print("No applications found in the specified period.", style="yellow")
        return
    
    # Count by status
    status_counts = {}
    for app in applications:
        status = app.status.replace("_", " ").title()
        status_counts[status] = status_counts.get(status, 0) + 1
    
    # Create status table
    status_table = Table(title="Application Status Breakdown")
    status_table.add_column("Status", style="cyan")
    status_table.add_column("Count", style="white")
    status_table.add_column("Percentage", style="green")
    
    total = len(applications)
    for status, count in sorted(status_counts.items()):
        percentage = (count / total * 100) if total > 0 else 0
        status_table.add_row(status, str(count), f"{percentage:.1f}%")
    
    console.print(status_table)


def display_detailed_applications(applications: list) -> None:
    """Display detailed application list."""
    if not applications:
        return
    
    # Sort by application date (most recent first)
    sorted_apps = sorted(applications, key=lambda x: x.application_date, reverse=True)
    
    # Create detailed table
    detail_table = Table(title="Recent Applications")
    detail_table.add_column("Date", style="cyan")
    detail_table.add_column("Job Title", style="white")
    detail_table.add_column("Company", style="white")
    detail_table.add_column("Status", style="green")
    detail_table.add_column("Method", style="dim")
    detail_table.add_column("Days", style="yellow")
    
    for app in sorted_apps[:20]:  # Show last 20 applications
        date_str = app.application_date.strftime("%m/%d")
        status = app.status.replace("_", " ").title()
        method = app.application_method.value.replace("_", " ").title()
        days_ago = app.get_days_since_application()
        
        # Color code status
        if app.status in ["submitted", "under_review"]:
            status_style = "green"
        elif app.status in ["rejected", "failed"]:
            status_style = "red"
        elif app.status in ["interview", "offer_received"]:
            status_style = "blue"
        else:
            status_style = "white"
        
        detail_table.add_row(
            date_str,
            app.job_title[:30] + "..." if len(app.job_title) > 30 else app.job_title,
            app.company_name[:20] + "..." if len(app.company_name) > 20 else app.company_name,
            f"[{status_style}]{status}[/{status_style}]",
            method,
            f"{days_ago}d"
        )
    
    console.print(detail_table)


def display_follow_up_needed(applications: list) -> None:
    """Display applications that need follow-up."""
    follow_up_apps = [app for app in applications if app.needs_follow_up()]
    
    if not follow_up_apps:
        console.print("✅ No applications need follow-up at this time.", style="green")
        return
    
    follow_up_table = Table(title="Applications Needing Follow-up")
    follow_up_table.add_column("Job Title", style="cyan")
    follow_up_table.add_column("Company", style="white")
    follow_up_table.add_column("Status", style="yellow")
    follow_up_table.add_column("Days Since Applied", style="red")
    follow_up_table.add_column("Last Activity", style="dim")
    
    for app in follow_up_apps:
        last_activity_days = app.get_last_activity_days()
        
        follow_up_table.add_row(
            app.job_title[:30] + "..." if len(app.job_title) > 30 else app.job_title,
            app.company_name,
            app.status.replace("_", " ").title(),
            f"{app.get_days_since_application()}",
            f"{last_activity_days}d ago"
        )
    
    console.print(follow_up_table)


def display_notion_statistics(stats: dict) -> None:
    """Display Notion database statistics."""
    notion_text = f"""
    📊 Total Applications: {stats.get('total_applications', 0)}
    📅 This Month: {stats.get('recent_applications', 0)}
    📞 Interview Rate: {stats.get('interview_rate', 0)}%
    📬 Response Rate: {stats.get('response_rate', 0)}%
    """
    
    console.print(Panel(
        notion_text.strip(),
        title="Notion Database Statistics",
        border_style="green"
    ))


def display_recommendations(applications: list, user_profile) -> None:
    """Display recommendations based on application data."""
    recommendations = []
    
    if not applications:
        recommendations.append("Start applying to jobs to see personalized recommendations")
    else:
        # Analyze success rate
        total_apps = len(applications)
        successful_apps = len([app for app in applications if app.status == "submitted"])
        success_rate = (successful_apps / total_apps * 100) if total_apps > 0 else 0
        
        if success_rate < 50:
            recommendations.append("Consider reviewing your application answers and resume")
        
        # Check for follow-ups needed
        follow_up_count = len([app for app in applications if app.needs_follow_up()])
        if follow_up_count > 0:
            recommendations.append(f"Follow up on {follow_up_count} pending applications")
        
        # Check daily application rate
        recent_apps = [
            app for app in applications 
            if app.application_date >= datetime.utcnow() - timedelta(days=7)
        ]
        avg_daily = len(recent_apps) / 7
        max_daily = user_profile.job_preferences.max_applications_per_day
        
        if avg_daily < max_daily * 0.5:
            recommendations.append("Consider increasing your daily application target")
        
        # Check for automation errors
        error_apps = [app for app in applications if app.automation_errors]
        if error_apps:
            recommendations.append(f"Review {len(error_apps)} applications with automation errors")
    
    if recommendations:
        rec_text = "\n".join(f"• {rec}" for rec in recommendations)
        console.print(Panel(
            rec_text,
            title="💡 Recommendations",
            border_style="yellow"
        ))
    else:
        console.print("✅ Everything looks good! Keep up the great work.", style="green")
