"""Company website automation using browser-use library."""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from .base_automation import BaseAutomation, AutomationError
from ..models.job_posting import JobPosting, JobSource
from ..models.application import Application, ApplicationMethod
from ..models.user_profile import UserProfile

logger = logging.getLogger(__name__)


class CompanyWebsiteAutomation(BaseAutomation):
    """Generic company website job application automation."""
    
    def __init__(self, user_profile: UserProfile):
        """Initialize company website automation."""
        super().__init__(user_profile)
        
    async def login(self) -> bool:
        """No login required for company websites."""
        return True
    
    async def search_jobs(self, keywords: List[str], location: str, 
                         filters: Dict[str, Any] = None) -> List[JobPosting]:
        """Company websites don't have search - jobs come from other platforms."""
        return []
    
    async def apply_to_job(self, job_posting: JobPosting) -> Application:
        """Apply to a job on a company website."""
        try:
            # Navigate to the job posting URL
            navigation_task = f"Navigate to {job_posting.url}"
            await self.agent.run(navigation_task)
            
            # Look for application form or apply button
            apply_task = """
            Look for an application form, 'Apply Now' button, or similar on this page.
            If there's a form, fill it out with the user's information.
            If there's an apply button, click it to proceed.
            """
            
            await self.agent.run(apply_task)
            
            # Try to fill out the application
            return await self._fill_company_application(job_posting)
            
        except Exception as e:
            logger.error(f"Company website application failed: {e}")
            application = self.create_application_record(
                job_posting, ApplicationMethod.COMPANY_WEBSITE, success=False
            )
            application.add_automation_error(str(e))
            return application
    
    async def _fill_company_application(self, job_posting: JobPosting) -> Application:
        """Fill out a generic company application form."""
        try:
            # Fill personal information
            personal_task = f"""
            Fill out any personal information fields with:
            - Name: {self.user_profile.personal_info.full_name}
            - Email: {self.user_profile.personal_info.email}
            - Phone: {self.user_profile.personal_info.phone or ""}
            - Location: {self.user_profile.personal_info.location}
            """
            
            await self.agent.run(personal_task)
            
            # Upload resume if possible
            if self.user_profile.resume_path:
                await self.upload_resume(self.user_profile.resume_path)
            
            # Answer common questions
            questions_task = f"""
            Look for any text areas or questions and answer them appropriately:
            - Cover letter or why interested: {self.user_profile.application_answers.why_interested}
            - Why good fit: {self.user_profile.application_answers.why_good_fit}
            - Experience: {self.user_profile.application_answers.relevant_experience}
            """
            
            await self.agent.run(questions_task)
            
            # Submit application
            submit_success = await self.submit_application()
            
            application = self.create_application_record(
                job_posting, ApplicationMethod.COMPANY_WEBSITE, success=submit_success
            )
            
            if submit_success:
                logger.info(f"Successfully applied to {job_posting.title} on company website")
            else:
                application.add_automation_error("Application submission failed")
            
            return application
            
        except Exception as e:
            logger.error(f"Company application form filling failed: {e}")
            raise AutomationError(f"Company application failed: {e}")
