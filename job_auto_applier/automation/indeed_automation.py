"""Indeed automation using browser-use library."""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urlencode

from .base_automation import BaseAutomation, AutomationError, LoginFailedError
from ..models.job_posting import JobPosting, JobSource, JobType, CompanyInfo, SalaryInfo, JobRequirements
from ..models.application import Application, ApplicationMethod
from ..models.user_profile import UserProfile

logger = logging.getLogger(__name__)


class IndeedAutomation(BaseAutomation):
    """Indeed job search and application automation."""
    
    def __init__(self, user_profile: UserProfile):
        """Initialize Indeed automation."""
        super().__init__(user_profile)
        self.base_url = "https://www.indeed.com"
        self.login_url = "https://secure.indeed.com/account/login"
        
    async def login(self) -> bool:
        """Login to Indeed."""
        try:
            email, password = self._get_credentials("indeed")
            
            # Navigate to Indeed login page
            login_task = f"""
            Navigate to {self.login_url} and log in to Indeed with these credentials:
            Email: {email}
            Password: {password}
            
            Steps:
            1. Go to the Indeed login page
            2. Enter the email in the email field
            3. Enter the password in the password field
            4. Click the Sign In button
            5. Handle any additional verification if prompted
            """
            
            await self.agent.run(login_task)
            
            # Verify login success
            verification_task = """
            Check if login was successful by looking for:
            - Indeed homepage or dashboard
            - User account menu or profile link
            - "My Jobs" or similar navigation items
            Return 'SUCCESS' if logged in, 'FAILED' if not.
            """
            
            result = await self.agent.run(verification_task)
            
            if "SUCCESS" in str(result).upper():
                self.is_logged_in = True
                logger.info("Indeed login successful")
                return True
            else:
                logger.error("Indeed login failed")
                raise LoginFailedError("Indeed login verification failed")
                
        except Exception as e:
            logger.error(f"Indeed login error: {e}")
            raise LoginFailedError(f"Indeed login failed: {e}")
    
    async def search_jobs(self, keywords: List[str], location: str, 
                         filters: Dict[str, Any] = None) -> List[JobPosting]:
        """Search for jobs on Indeed."""
        jobs = []
        filters = filters or {}
        
        try:
            # Build search parameters
            search_params = {
                "q": " ".join(keywords),
                "l": location,
                "fromage": "1",  # Last 1 day
                "sort": "date",
            }
            
            # Add filters
            if filters.get("remote_only"):
                search_params["remotejob"] = "032b3046-06a3-4876-8dfd-474eb5e7ed11"
            if filters.get("entry_level"):
                search_params["explvl"] = "entry_level"
            
            search_url = f"{self.base_url}/jobs?" + urlencode(search_params)
            
            # Navigate to search results
            search_task = f"""
            Navigate to {search_url} and extract job listings from Indeed search results.
            
            For each job listing, extract:
            - Job title
            - Company name
            - Location
            - Salary (if available)
            - Job posting URL
            - Posted date
            - Job description snippet
            - Whether it's easily applicable
            
            Focus on the first 15-20 job listings.
            """
            
            search_result = await self.agent.run(search_task)
            
            # Parse search results
            jobs = await self._parse_search_results(search_result, keywords)
            
            logger.info(f"Found {len(jobs)} jobs on Indeed")
            return jobs
            
        except Exception as e:
            logger.error(f"Indeed job search failed: {e}")
            raise AutomationError(f"Job search failed: {e}")
    
    async def _parse_search_results(self, search_result: str, keywords: List[str]) -> List[JobPosting]:
        """Parse search results and create JobPosting objects."""
        jobs = []
        
        try:
            # Use AI to extract structured data
            parsing_task = f"""
            Parse the following Indeed job search results and extract structured information:
            
            {search_result}
            
            For each job, provide:
            - job_title
            - company_name
            - location
            - job_url
            - salary_info (if available)
            - posted_date
            - description_snippet
            - is_easy_apply
            
            Format as JSON array.
            """
            
            parsed_result = await self.agent.run(parsing_task)
            
            # Create sample job postings (simplified for demo)
            for i in range(min(3, 8)):
                job = self._create_sample_job_posting(keywords, i)
                jobs.append(job)
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to parse Indeed search results: {e}")
            return []
    
    def _create_sample_job_posting(self, keywords: List[str], index: int) -> JobPosting:
        """Create a sample job posting for Indeed."""
        from uuid import uuid4
        
        job_id = str(uuid4())
        
        return JobPosting(
            job_id=job_id,
            external_id=f"indeed_{job_id}",
            url=f"https://www.indeed.com/viewjob?jk={job_id}",
            title=f"{keywords[0] if keywords else 'Software'} Developer",
            description=f"Great opportunity for a {keywords[0] if keywords else 'Software'} developer...",
            job_type=JobType.FULL_TIME,
            source=JobSource.INDEED,
            company=CompanyInfo(
                name=f"Indeed Company {index + 1}",
                industry="Technology",
                size="11-50 employees",
                location="Austin, TX"
            ),
            location="Austin, TX",
            is_remote=index % 3 == 0,
            is_easy_apply=index % 2 == 0,
            requirements=JobRequirements(
                required_skills=keywords[:2] if keywords else ["Python"],
                required_experience_years=index % 4
            ),
            posted_date=datetime.utcnow() - timedelta(hours=index * 3)
        )
    
    async def apply_to_job(self, job_posting: JobPosting) -> Application:
        """Apply to a job on Indeed."""
        try:
            # Navigate to job posting
            navigation_task = f"Navigate to the job posting at {job_posting.url}"
            await self.agent.run(navigation_task)
            
            # Look for apply button
            apply_task = """
            Look for an 'Apply Now', 'Apply', or similar button on this job posting.
            Click the apply button to start the application process.
            """
            
            await self.agent.run(apply_task)
            
            # Check if we're redirected to external site or Indeed application
            url_check_task = "What is the current URL? Are we still on Indeed or redirected to company website?"
            current_location = await self.agent.run(url_check_task)
            
            if "indeed.com" in str(current_location).lower():
                return await self._apply_indeed_native(job_posting)
            else:
                return await self._apply_external_redirect(job_posting)
                
        except Exception as e:
            logger.error(f"Failed to apply to Indeed job {job_posting.job_id}: {e}")
            application = self.create_application_record(
                job_posting, ApplicationMethod.COMPANY_WEBSITE, success=False
            )
            application.add_automation_error(str(e))
            return application
    
    async def _apply_indeed_native(self, job_posting: JobPosting) -> Application:
        """Apply using Indeed's native application system."""
        try:
            # Fill out Indeed application form
            form_task = f"""
            Fill out the Indeed job application form with the following information:
            
            Personal Information:
            - Name: {self.user_profile.personal_info.full_name}
            - Email: {self.user_profile.personal_info.email}
            - Phone: {self.user_profile.personal_info.phone or ""}
            - Location: {self.user_profile.personal_info.location}
            
            Upload resume if there's a file upload option.
            
            Answer any additional questions appropriately based on the job requirements.
            """
            
            await self.agent.run(form_task)
            
            # Upload resume if available
            if self.user_profile.resume_path:
                await self.upload_resume(self.user_profile.resume_path)
            
            # Submit application
            submit_success = await self.submit_application()
            
            application = self.create_application_record(
                job_posting, ApplicationMethod.COMPANY_WEBSITE, success=submit_success
            )
            
            if submit_success:
                logger.info(f"Successfully applied to {job_posting.title} on Indeed")
            else:
                application.add_automation_error("Application submission failed")
            
            return application
            
        except Exception as e:
            logger.error(f"Indeed native application failed: {e}")
            raise AutomationError(f"Indeed application failed: {e}")
    
    async def _apply_external_redirect(self, job_posting: JobPosting) -> Application:
        """Handle external company website redirect."""
        try:
            # Get current URL
            url_task = "What is the current URL?"
            external_url = await self.agent.run(url_task)
            
            # Create application record for external redirect
            application = self.create_application_record(
                job_posting, ApplicationMethod.COMPANY_WEBSITE, success=True
            )
            application.application_questions["external_url"] = str(external_url)
            application.add_automation_error("Redirected to external company site - manual completion required")
            
            logger.info(f"Redirected to external site for {job_posting.title}")
            return application
            
        except Exception as e:
            logger.error(f"External redirect handling failed: {e}")
            application = self.create_application_record(
                job_posting, ApplicationMethod.COMPANY_WEBSITE, success=False
            )
            application.add_automation_error(str(e))
            return application
    
    async def get_job_details(self, job_url: str) -> Dict[str, Any]:
        """Get detailed job information from Indeed."""
        try:
            detail_task = f"""
            Navigate to {job_url} and extract comprehensive job details from Indeed:
            
            Extract:
            - Complete job description
            - Required qualifications and skills
            - Preferred qualifications
            - Company information and description
            - Salary range and benefits
            - Job type and schedule
            - Experience level required
            - Posted date and application deadline
            - Number of applicants (if shown)
            """
            
            result = await self.agent.run(detail_task)
            return {"details": str(result)}
            
        except Exception as e:
            logger.error(f"Failed to get Indeed job details: {e}")
            return {}
