"""LinkedIn automation using browser-use library."""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urlencode

from .base_automation import BaseAutomation, AutomationError, LoginFailedError
from ..models.job_posting import JobPosting, JobSource, JobType, CompanyInfo, SalaryInfo, JobRequirements
from ..models.application import Application, ApplicationMethod
from ..models.user_profile import UserProfile

logger = logging.getLogger(__name__)


class LinkedInAutomation(BaseAutomation):
    """LinkedIn job search and application automation."""
    
    def __init__(self, user_profile: UserProfile):
        """Initialize LinkedIn automation."""
        super().__init__(user_profile)
        self.base_url = "https://www.linkedin.com"
        self.jobs_url = "https://www.linkedin.com/jobs"
        self.login_url = "https://www.linkedin.com/login"
        
    async def login(self) -> bool:
        """Login to LinkedIn."""
        try:
            email, password = self._get_credentials("linkedin")
            
            # Navigate to LinkedIn login page
            login_task = f"""
            Navigate to {self.login_url} and log in to LinkedIn with these credentials:
            Email: {email}
            Password: {password}
            
            Steps:
            1. Go to the LinkedIn login page
            2. Enter the email in the email field
            3. Enter the password in the password field
            4. Click the Sign In button
            5. Handle any additional verification if prompted
            """
            
            await self.agent.run(login_task)
            
            # Verify login success
            verification_task = """
            Check if login was successful by looking for:
            - LinkedIn homepage elements
            - User profile picture or name in the header
            - Navigation menu items like 'My Network', 'Jobs', etc.
            Return 'SUCCESS' if logged in, 'FAILED' if not.
            """
            
            result = await self.agent.run(verification_task)
            
            if "SUCCESS" in str(result).upper():
                self.is_logged_in = True
                logger.info("LinkedIn login successful")
                return True
            else:
                logger.error("LinkedIn login failed")
                raise LoginFailedError("LinkedIn login verification failed")
                
        except Exception as e:
            logger.error(f"LinkedIn login error: {e}")
            raise LoginFailedError(f"LinkedIn login failed: {e}")
    
    async def search_jobs(self, keywords: List[str], location: str, 
                         filters: Dict[str, Any] = None) -> List[JobPosting]:
        """Search for jobs on LinkedIn."""
        if not self.is_logged_in:
            await self.login()
        
        jobs = []
        filters = filters or {}
        
        try:
            # Build search parameters
            search_params = {
                "keywords": " ".join(keywords),
                "location": location,
                "f_TPR": "r86400",  # Past 24 hours
                "f_E": "1,2",  # Entry level and Associate
            }
            
            # Add additional filters
            if filters.get("remote_only"):
                search_params["f_WT"] = "2"  # Remote
            if filters.get("easy_apply_only"):
                search_params["f_AL"] = "true"  # Easy Apply
            
            search_url = f"{self.jobs_url}/search?" + urlencode(search_params)
            
            # Navigate to search results
            search_task = f"""
            Navigate to {search_url} and extract job listings from the search results.
            
            For each job listing, extract:
            - Job title
            - Company name
            - Location
            - Job posting URL
            - Whether it has Easy Apply
            - Posted date
            - Brief description/snippet
            
            Focus on the first 10-20 job listings.
            """
            
            search_result = await self.agent.run(search_task)
            
            # Parse search results and create JobPosting objects
            jobs = await self._parse_search_results(search_result, keywords)
            
            logger.info(f"Found {len(jobs)} jobs on LinkedIn")
            return jobs
            
        except Exception as e:
            logger.error(f"LinkedIn job search failed: {e}")
            raise AutomationError(f"Job search failed: {e}")
    
    async def _parse_search_results(self, search_result: str, keywords: List[str]) -> List[JobPosting]:
        """Parse search results and create JobPosting objects."""
        jobs = []
        
        try:
            # Use AI to extract structured data from search results
            parsing_task = f"""
            Parse the following LinkedIn job search results and extract structured information for each job:
            
            {search_result}
            
            For each job, provide:
            - job_title
            - company_name
            - location
            - job_url
            - is_easy_apply (true/false)
            - posted_date
            - description_snippet
            
            Format as JSON array.
            """
            
            parsed_result = await self.agent.run(parsing_task)
            
            # This would need proper JSON parsing in a real implementation
            # For now, we'll create sample job postings
            
            # Create JobPosting objects (simplified for demo)
            for i in range(min(5, 10)):  # Limit to reasonable number
                job = self._create_sample_job_posting(keywords, i)
                jobs.append(job)
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to parse search results: {e}")
            return []
    
    def _create_sample_job_posting(self, keywords: List[str], index: int) -> JobPosting:
        """Create a sample job posting (for demonstration)."""
        from uuid import uuid4
        
        job_id = str(uuid4())
        
        return JobPosting(
            job_id=job_id,
            external_id=f"linkedin_{job_id}",
            url=f"https://www.linkedin.com/jobs/view/{job_id}",
            title=f"Software Engineer - {keywords[0] if keywords else 'Python'}",
            description=f"Exciting opportunity for a {keywords[0] if keywords else 'Python'} developer...",
            job_type=JobType.FULL_TIME,
            source=JobSource.LINKEDIN,
            company=CompanyInfo(
                name=f"Tech Company {index + 1}",
                industry="Technology",
                size="51-200 employees",
                location="San Francisco, CA"
            ),
            location="San Francisco, CA",
            is_remote=index % 2 == 0,
            is_easy_apply=True,
            requirements=JobRequirements(
                required_skills=keywords[:3] if keywords else ["Python", "JavaScript"],
                required_experience_years=1 + index % 3
            ),
            posted_date=datetime.utcnow() - timedelta(hours=index * 2)
        )
    
    async def apply_to_job(self, job_posting: JobPosting) -> Application:
        """Apply to a job on LinkedIn."""
        if not self.is_logged_in:
            await self.login()
        
        try:
            # Navigate to job posting
            navigation_task = f"Navigate to the job posting at {job_posting.url}"
            await self.agent.run(navigation_task)
            
            # Check if Easy Apply is available
            if job_posting.is_easy_apply:
                return await self._apply_easy_apply(job_posting)
            else:
                return await self._apply_external(job_posting)
                
        except Exception as e:
            logger.error(f"Failed to apply to job {job_posting.job_id}: {e}")
            application = self.create_application_record(
                job_posting, ApplicationMethod.EASY_APPLY, success=False
            )
            application.add_automation_error(str(e))
            return application
    
    async def _apply_easy_apply(self, job_posting: JobPosting) -> Application:
        """Apply using LinkedIn Easy Apply."""
        try:
            # Click Easy Apply button
            easy_apply_task = """
            Find and click the 'Easy Apply' button on this job posting.
            This will open the Easy Apply modal/form.
            """
            await self.agent.run(easy_apply_task)
            
            # Fill out the application form
            await self._fill_easy_apply_form(job_posting)
            
            # Submit application
            submit_task = """
            Find and click the 'Submit application' or 'Send application' button
            to complete the Easy Apply process.
            """
            await self.agent.run(submit_task)
            
            # Verify submission
            await asyncio.sleep(3)
            verification_task = """
            Check if the application was submitted successfully.
            Look for confirmation messages like 'Application sent' or 'Your application was submitted'.
            """
            result = await self.agent.run(verification_task)
            
            success = "sent" in str(result).lower() or "submitted" in str(result).lower()
            
            application = self.create_application_record(
                job_posting, ApplicationMethod.EASY_APPLY, success=success
            )
            
            if success:
                logger.info(f"Successfully applied to {job_posting.title} at {job_posting.company.name}")
            else:
                application.add_automation_error("Application submission verification failed")
            
            return application
            
        except Exception as e:
            logger.error(f"Easy Apply failed: {e}")
            raise AutomationError(f"Easy Apply failed: {e}")
    
    async def _fill_easy_apply_form(self, job_posting: JobPosting) -> None:
        """Fill out the Easy Apply form."""
        try:
            # Upload resume if required
            if self.user_profile.resume_path:
                upload_task = f"""
                Look for a resume upload section in the Easy Apply form.
                If found, upload the resume file from {self.user_profile.resume_path}.
                """
                await self.agent.run(upload_task)
            
            # Fill out additional questions
            questions_task = f"""
            Look for any additional questions in the Easy Apply form and answer them appropriately:
            
            Common questions and answers:
            - "Why are you interested in this role?": "{self.user_profile.application_answers.why_interested}"
            - "What makes you a good fit?": "{self.user_profile.application_answers.why_good_fit}"
            - "Years of experience": Based on the job requirements
            - "Available start date": "{self.user_profile.application_answers.available_start_date}"
            - "Willing to relocate?": "{'Yes' if self.user_profile.application_answers.willing_to_relocate else 'No'}"
            - "Require sponsorship?": "{'Yes' if self.user_profile.application_answers.requires_sponsorship else 'No'}"
            
            Fill out all required fields appropriately.
            """
            await self.agent.run(questions_task)
            
        except Exception as e:
            logger.error(f"Failed to fill Easy Apply form: {e}")
            raise
    
    async def _apply_external(self, job_posting: JobPosting) -> Application:
        """Apply through external company website."""
        try:
            # Find and click the external apply button
            external_task = """
            Look for an 'Apply' button that redirects to the company's website.
            Click this button to go to the external application page.
            """
            await self.agent.run(external_task)
            
            # Wait for redirect
            await asyncio.sleep(3)
            
            # Get current URL to determine if we're on external site
            url_task = "What is the current URL of the page?"
            current_url = await self.agent.run(url_task)
            
            # Create application record for external application
            application = self.create_application_record(
                job_posting, ApplicationMethod.COMPANY_WEBSITE, success=True
            )
            application.application_questions["external_url"] = str(current_url)
            application.add_automation_error("Redirected to external site - manual completion required")
            
            logger.info(f"Redirected to external application for {job_posting.title}")
            return application
            
        except Exception as e:
            logger.error(f"External application failed: {e}")
            application = self.create_application_record(
                job_posting, ApplicationMethod.COMPANY_WEBSITE, success=False
            )
            application.add_automation_error(str(e))
            return application
    
    async def get_job_details(self, job_url: str) -> Dict[str, Any]:
        """Get detailed job information from LinkedIn."""
        try:
            detail_task = f"""
            Navigate to {job_url} and extract comprehensive job details:
            
            Extract:
            - Full job description
            - Required qualifications
            - Preferred qualifications
            - Company information
            - Salary range (if available)
            - Benefits mentioned
            - Job type (full-time, part-time, contract)
            - Experience level required
            - Industry
            - Company size
            - Posted date
            - Application deadline (if any)
            """
            
            result = await self.agent.run(detail_task)
            return {"details": str(result)}
            
        except Exception as e:
            logger.error(f"Failed to get LinkedIn job details: {e}")
            return {}
    
    async def check_application_status(self, application_id: str) -> str:
        """Check the status of a submitted application."""
        try:
            # Navigate to LinkedIn applications page
            applications_url = "https://www.linkedin.com/my-items/saved-jobs/"
            
            status_task = f"""
            Navigate to {applications_url} and find the application with ID {application_id}.
            Check the current status of this application.
            """
            
            result = await self.agent.run(status_task)
            return str(result)
            
        except Exception as e:
            logger.error(f"Failed to check application status: {e}")
            return "unknown"
