"""Base automation class using browser-use library."""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

from browser_use import Agent
from langchain_openai import ChatOpenAI
from langchain_anthropic import <PERSON>t<PERSON>nthropic

from ..config.settings import get_settings
from ..config.encryption import EncryptionManager
from ..models.job_posting import JobPosting
from ..models.application import Application, ApplicationMethod
from ..models.user_profile import UserProfile

logger = logging.getLogger(__name__)


class AutomationError(Exception):
    """Custom exception for automation errors."""
    pass


class CaptchaDetectedError(AutomationError):
    """Exception raised when CAPTCHA is detected."""
    pass


class LoginFailedError(AutomationError):
    """Exception raised when login fails."""
    pass


class BaseAutomation(ABC):
    """Base class for all job portal automation."""
    
    def __init__(self, user_profile: UserProfile):
        """Initialize base automation."""
        self.user_profile = user_profile
        self.settings = get_settings()
        self.encryption_manager = EncryptionManager()
        self.agent: Optional[Agent] = None
        self.is_logged_in = False
        
        # Initialize AI model based on settings
        self.llm = self._initialize_llm()
        
        # Automation settings
        self.max_retries = 3
        self.delay_between_actions = 2
        self.screenshot_dir = self.settings.data_dir / "screenshots"
        self.screenshot_dir.mkdir(exist_ok=True)
        
    def _initialize_llm(self):
        """Initialize the appropriate LLM based on settings."""
        provider = self.settings.get_ai_provider()
        
        if provider == "openai":
            return ChatOpenAI(
                model="gpt-4o",
                api_key=self.settings.openai_api_key,
                temperature=0.1
            )
        elif provider == "anthropic":
            return ChatAnthropic(
                model="claude-3-sonnet-20240229",
                api_key=self.settings.anthropic_api_key,
                temperature=0.1
            )
        else:
            raise ValueError(f"Unsupported AI provider: {provider}")
    
    async def initialize_browser(self) -> None:
        """Initialize browser agent."""
        try:
            self.agent = Agent(
                task="Job application automation",
                llm=self.llm,
                headless=self.settings.headless_mode,
                timeout=self.settings.browser_timeout
            )
            logger.info("Browser agent initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize browser agent: {e}")
            raise AutomationError(f"Browser initialization failed: {e}")
    
    async def close_browser(self) -> None:
        """Close browser and cleanup."""
        if self.agent:
            try:
                # Browser-use handles cleanup automatically
                self.agent = None
                logger.info("Browser closed successfully")
            except Exception as e:
                logger.warning(f"Error closing browser: {e}")
    
    async def take_screenshot(self, filename: Optional[str] = None) -> Path:
        """Take a screenshot for debugging."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
        
        screenshot_path = self.screenshot_dir / filename
        
        try:
            # Browser-use doesn't expose direct screenshot functionality
            # This would need to be implemented using playwright directly
            logger.info(f"Screenshot saved to {screenshot_path}")
            return screenshot_path
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
            raise
    
    def _get_credentials(self, platform: str) -> Tuple[str, str]:
        """Get encrypted credentials for a platform."""
        email_key = f"{platform}_email"
        password_key = f"{platform}_password"
        
        email = getattr(self.user_profile, email_key, None)
        if not email:
            raise AutomationError(f"No email configured for {platform}")
        
        password = self.encryption_manager.get_credential(email, platform)
        if not password:
            raise AutomationError(f"No password found for {platform} account {email}")
        
        return email, password
    
    async def detect_captcha(self) -> bool:
        """Detect if CAPTCHA is present on the page."""
        try:
            # Use browser-use agent to detect CAPTCHA
            task = "Check if there is a CAPTCHA or human verification challenge on this page"
            result = await self.agent.run(task)
            
            # Parse the result to determine if CAPTCHA is present
            captcha_indicators = ["captcha", "recaptcha", "human verification", "prove you're not a robot"]
            result_lower = str(result).lower()
            
            return any(indicator in result_lower for indicator in captcha_indicators)
        except Exception as e:
            logger.warning(f"Error detecting CAPTCHA: {e}")
            return False
    
    async def handle_captcha(self) -> bool:
        """Handle CAPTCHA detection."""
        logger.warning("CAPTCHA detected - manual intervention required")
        
        if self.settings.screenshot_on_error:
            await self.take_screenshot("captcha_detected.png")
        
        # In a production system, you might:
        # 1. Send notification to user
        # 2. Pause automation and wait for manual resolution
        # 3. Use CAPTCHA solving service
        
        raise CaptchaDetectedError("CAPTCHA detected - manual intervention required")
    
    async def wait_and_retry(self, operation, max_retries: int = None) -> Any:
        """Retry an operation with exponential backoff."""
        max_retries = max_retries or self.max_retries
        
        for attempt in range(max_retries):
            try:
                return await operation()
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                
                wait_time = (2 ** attempt) * self.delay_between_actions
                logger.warning(f"Operation failed (attempt {attempt + 1}/{max_retries}): {e}")
                logger.info(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
    
    @abstractmethod
    async def login(self) -> bool:
        """Login to the job portal."""
        pass
    
    @abstractmethod
    async def search_jobs(self, keywords: List[str], location: str, 
                         filters: Dict[str, Any] = None) -> List[JobPosting]:
        """Search for jobs on the platform."""
        pass
    
    @abstractmethod
    async def apply_to_job(self, job_posting: JobPosting) -> Application:
        """Apply to a specific job."""
        pass
    
    async def get_job_details(self, job_url: str) -> Dict[str, Any]:
        """Get detailed information about a job posting."""
        try:
            task = f"Navigate to {job_url} and extract all job details including title, company, description, requirements, salary, and application method"
            result = await self.agent.run(task)
            return {"details": str(result)}
        except Exception as e:
            logger.error(f"Failed to get job details from {job_url}: {e}")
            return {}
    
    async def fill_application_form(self, questions: Dict[str, str]) -> Dict[str, str]:
        """Fill out application form with provided answers."""
        responses = {}
        
        try:
            for question, answer in questions.items():
                task = f"Find the form field for '{question}' and fill it with '{answer}'"
                await self.agent.run(task)
                responses[question] = answer
                
                # Add delay between form fields
                await asyncio.sleep(self.delay_between_actions)
            
            return responses
        except Exception as e:
            logger.error(f"Failed to fill application form: {e}")
            raise AutomationError(f"Form filling failed: {e}")
    
    async def upload_resume(self, resume_path: Path) -> bool:
        """Upload resume to the application form."""
        try:
            task = f"Find the resume upload button or file input and upload the file from {resume_path}"
            await self.agent.run(task)
            
            logger.info(f"Resume uploaded successfully: {resume_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to upload resume: {e}")
            return False
    
    async def submit_application(self) -> bool:
        """Submit the completed application."""
        try:
            task = "Find and click the submit button to submit the job application"
            await self.agent.run(task)
            
            # Wait for submission confirmation
            await asyncio.sleep(3)
            
            # Check for success confirmation
            confirmation_task = "Check if the application was submitted successfully by looking for confirmation messages"
            result = await self.agent.run(confirmation_task)
            
            success_indicators = ["success", "submitted", "application sent", "thank you"]
            result_lower = str(result).lower()
            
            return any(indicator in result_lower for indicator in success_indicators)
        except Exception as e:
            logger.error(f"Failed to submit application: {e}")
            return False
    
    def create_application_record(self, job_posting: JobPosting, 
                                 application_method: ApplicationMethod,
                                 success: bool = True) -> Application:
        """Create an application record."""
        from uuid import uuid4
        
        application = Application(
            application_id=str(uuid4()),
            job_id=job_posting.job_id,
            user_id=self.user_profile.user_id,
            status="submitted" if success else "failed",
            application_method=application_method,
            job_title=job_posting.title,
            company_name=job_posting.company.name,
            job_url=job_posting.url,
            automated_application=True
        )
        
        if not success:
            application.add_automation_error("Application submission failed")
        
        return application
    
    async def run_automation_safely(self, operation, *args, **kwargs) -> Any:
        """Run automation operation with error handling."""
        try:
            # Check for CAPTCHA before operation
            if await self.detect_captcha():
                await self.handle_captcha()
            
            return await operation(*args, **kwargs)
            
        except CaptchaDetectedError:
            raise
        except Exception as e:
            logger.error(f"Automation operation failed: {e}")
            
            if self.settings.screenshot_on_error:
                await self.take_screenshot(f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            
            raise AutomationError(f"Operation failed: {e}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_browser()
