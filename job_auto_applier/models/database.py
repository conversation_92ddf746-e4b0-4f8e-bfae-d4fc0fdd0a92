"""Database models and management using SQLAlchemy."""

import json
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import create_engine, Column, String, DateTime, Boolean, Integer, Text, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.postgresql import JSO<PERSON>
from sqlalchemy.types import TypeDecorator, VARCHAR
import logging

from ..config.settings import get_settings

logger = logging.getLogger(__name__)

Base = declarative_base()


class JSONEncodedDict(TypeDecorator):
    """Represents an immutable structure as a json-encoded string."""
    
    impl = VARCHAR
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            value = json.dumps(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            value = json.loads(value)
        return value


class UserProfileDB(Base):
    """Database model for user profiles."""
    
    __tablename__ = "user_profiles"
    
    user_id = Column(String(50), primary_key=True)
    personal_info = Column(JSONEncodedDict)
    job_preferences = Column(JSONEncodedDict)
    application_answers = Column(JSONEncodedDict)
    resume_path = Column(String(500))
    cover_letter_template = Column(Text)
    
    # Encrypted credentials
    linkedin_email = Column(String(255))
    indeed_email = Column(String(255))
    glassdoor_email = Column(String(255))
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_job_search = Column(DateTime)
    total_applications = Column(Integer, default=0)
    
    # Settings
    notifications_enabled = Column(Boolean, default=True)
    auto_apply_enabled = Column(Boolean, default=True)


class JobPostingDB(Base):
    """Database model for job postings."""
    
    __tablename__ = "job_postings"
    
    job_id = Column(String(100), primary_key=True)
    external_id = Column(String(100))
    url = Column(String(1000), nullable=False)
    
    # Basic job information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    job_type = Column(String(50), default="full_time")
    source = Column(String(50), nullable=False)
    
    # Company information
    company_info = Column(JSONEncodedDict)
    
    # Location
    location = Column(String(200), nullable=False)
    is_remote = Column(Boolean, default=False)
    is_hybrid = Column(Boolean, default=False)
    
    # Salary and compensation
    salary_info = Column(JSONEncodedDict)
    
    # Requirements
    requirements = Column(JSONEncodedDict)
    
    # Application information
    application_url = Column(String(1000))
    is_easy_apply = Column(Boolean, default=False)
    application_deadline = Column(DateTime)
    
    # Metadata
    posted_date = Column(DateTime)
    discovered_date = Column(DateTime, default=datetime.utcnow)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Matching and scoring
    match_score = Column(Float)
    keywords_matched = Column(JSONEncodedDict)
    
    # Status
    is_active = Column(Boolean, default=True)
    is_applied = Column(Boolean, default=False)
    application_date = Column(DateTime)


class ApplicationDB(Base):
    """Database model for job applications."""
    
    __tablename__ = "applications"
    
    application_id = Column(String(100), primary_key=True)
    job_id = Column(String(100), nullable=False)
    user_id = Column(String(50), nullable=False)
    
    # Application details
    status = Column(String(50), default="pending")
    application_method = Column(String(50), nullable=False)
    application_date = Column(DateTime, default=datetime.utcnow)
    
    # Job information (cached)
    job_title = Column(String(200), nullable=False)
    company_name = Column(String(200), nullable=False)
    job_url = Column(String(1000), nullable=False)
    
    # Application materials
    resume_version = Column(String(100))
    cover_letter_used = Column(Boolean, default=False)
    cover_letter_content = Column(Text)
    
    # Questions and responses
    application_questions = Column(JSONEncodedDict)
    custom_responses = Column(JSONEncodedDict)
    
    # Communication tracking
    responses = Column(JSONEncodedDict)
    last_contact_date = Column(DateTime)
    next_follow_up_date = Column(DateTime)
    
    # Interview tracking
    interviews_scheduled = Column(Integer, default=0)
    interviews_completed = Column(Integer, default=0)
    current_interview_stage = Column(String(100))
    
    # Outcome tracking
    rejection_reason = Column(Text)
    offer_details = Column(JSONEncodedDict)
    salary_offered = Column(Float)
    
    # Automation metadata
    automated_application = Column(Boolean, default=True)
    automation_errors = Column(JSONEncodedDict)
    manual_intervention_required = Column(Boolean, default=False)
    
    # Notion integration
    notion_page_id = Column(String(100))
    notion_url = Column(String(1000))
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DatabaseManager:
    """Database management and operations."""
    
    def __init__(self, database_url: Optional[str] = None):
        """Initialize database manager."""
        self.settings = get_settings()
        self.database_url = database_url or self.settings.database_url
        self.engine = create_engine(self.database_url, echo=self.settings.debug)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
    def create_tables(self) -> None:
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get a database session."""
        return self.SessionLocal()
    
    def save_user_profile(self, user_profile) -> None:
        """Save user profile to database."""
        session = self.get_session()
        try:
            # Convert Pydantic model to database model
            db_profile = UserProfileDB(
                user_id=user_profile.user_id,
                personal_info=user_profile.personal_info.dict(),
                job_preferences=user_profile.job_preferences.dict(),
                application_answers=user_profile.application_answers.dict(),
                resume_path=str(user_profile.resume_path) if user_profile.resume_path else None,
                cover_letter_template=user_profile.cover_letter_template,
                linkedin_email=user_profile.linkedin_email,
                indeed_email=user_profile.indeed_email,
                glassdoor_email=user_profile.glassdoor_email,
                created_at=user_profile.created_at,
                updated_at=user_profile.updated_at,
                last_job_search=user_profile.last_job_search,
                total_applications=user_profile.total_applications,
                notifications_enabled=user_profile.notifications_enabled,
                auto_apply_enabled=user_profile.auto_apply_enabled,
            )
            
            # Merge (insert or update)
            session.merge(db_profile)
            session.commit()
            logger.info(f"Saved user profile for {user_profile.user_id}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to save user profile: {e}")
            raise
        finally:
            session.close()
    
    def get_user_profile(self, user_id: str):
        """Get user profile from database."""
        session = self.get_session()
        try:
            db_profile = session.query(UserProfileDB).filter(UserProfileDB.user_id == user_id).first()
            if not db_profile:
                return None
            
            # Convert back to Pydantic model
            from ..models.user_profile import UserProfile, PersonalInfo, JobPreferences, ApplicationAnswers
            from pathlib import Path
            
            return UserProfile(
                user_id=db_profile.user_id,
                personal_info=PersonalInfo(**db_profile.personal_info),
                job_preferences=JobPreferences(**db_profile.job_preferences),
                application_answers=ApplicationAnswers(**db_profile.application_answers),
                resume_path=Path(db_profile.resume_path) if db_profile.resume_path else None,
                cover_letter_template=db_profile.cover_letter_template,
                linkedin_email=db_profile.linkedin_email,
                indeed_email=db_profile.indeed_email,
                glassdoor_email=db_profile.glassdoor_email,
                created_at=db_profile.created_at,
                updated_at=db_profile.updated_at,
                last_job_search=db_profile.last_job_search,
                total_applications=db_profile.total_applications,
                notifications_enabled=db_profile.notifications_enabled,
                auto_apply_enabled=db_profile.auto_apply_enabled,
            )
            
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            return None
        finally:
            session.close()
    
    def save_job_posting(self, job_posting) -> None:
        """Save job posting to database."""
        session = self.get_session()
        try:
            db_job = JobPostingDB(
                job_id=job_posting.job_id,
                external_id=job_posting.external_id,
                url=str(job_posting.url),
                title=job_posting.title,
                description=job_posting.description,
                job_type=job_posting.job_type.value,
                source=job_posting.source.value,
                company_info=job_posting.company.dict(),
                location=job_posting.location,
                is_remote=job_posting.is_remote,
                is_hybrid=job_posting.is_hybrid,
                salary_info=job_posting.salary.dict() if job_posting.salary else None,
                requirements=job_posting.requirements.dict(),
                application_url=str(job_posting.application_url) if job_posting.application_url else None,
                is_easy_apply=job_posting.is_easy_apply,
                application_deadline=job_posting.application_deadline,
                posted_date=job_posting.posted_date,
                discovered_date=job_posting.discovered_date,
                last_updated=job_posting.last_updated,
                match_score=job_posting.match_score,
                keywords_matched=job_posting.keywords_matched,
                is_active=job_posting.is_active,
                is_applied=job_posting.is_applied,
                application_date=job_posting.application_date,
            )
            
            session.merge(db_job)
            session.commit()
            logger.info(f"Saved job posting {job_posting.job_id}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to save job posting: {e}")
            raise
        finally:
            session.close()
    
    def save_application(self, application) -> None:
        """Save application to database."""
        session = self.get_session()
        try:
            db_app = ApplicationDB(
                application_id=application.application_id,
                job_id=application.job_id,
                user_id=application.user_id,
                status=application.status.value,
                application_method=application.application_method.value,
                application_date=application.application_date,
                job_title=application.job_title,
                company_name=application.company_name,
                job_url=str(application.job_url),
                resume_version=application.resume_version,
                cover_letter_used=application.cover_letter_used,
                cover_letter_content=application.cover_letter_content,
                application_questions=application.application_questions,
                custom_responses=application.custom_responses,
                responses=[r.dict() for r in application.responses],
                last_contact_date=application.last_contact_date,
                next_follow_up_date=application.next_follow_up_date,
                interviews_scheduled=application.interviews_scheduled,
                interviews_completed=application.interviews_completed,
                current_interview_stage=application.current_interview_stage,
                rejection_reason=application.rejection_reason,
                offer_details=application.offer_details,
                salary_offered=application.salary_offered,
                automated_application=application.automated_application,
                automation_errors=application.automation_errors,
                manual_intervention_required=application.manual_intervention_required,
                notion_page_id=application.notion_page_id,
                notion_url=str(application.notion_url) if application.notion_url else None,
                created_at=application.created_at,
                updated_at=application.updated_at,
            )
            
            session.merge(db_app)
            session.commit()
            logger.info(f"Saved application {application.application_id}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Failed to save application: {e}")
            raise
        finally:
            session.close()
    
    def get_applications_by_user(self, user_id: str, limit: Optional[int] = None) -> List:
        """Get applications for a user."""
        session = self.get_session()
        try:
            query = session.query(ApplicationDB).filter(ApplicationDB.user_id == user_id)
            if limit:
                query = query.limit(limit)
            return query.all()
        finally:
            session.close()
    
    def get_recent_job_postings(self, days: int = 7, limit: Optional[int] = None) -> List:
        """Get recent job postings."""
        session = self.get_session()
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            query = session.query(JobPostingDB).filter(
                JobPostingDB.discovered_date >= cutoff_date,
                JobPostingDB.is_active == True
            ).order_by(JobPostingDB.discovered_date.desc())
            
            if limit:
                query = query.limit(limit)
            return query.all()
        finally:
            session.close()


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
        _db_manager.create_tables()
    return _db_manager
