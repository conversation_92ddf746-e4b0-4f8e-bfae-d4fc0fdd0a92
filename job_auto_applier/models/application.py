"""Application tracking data models."""

from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, HttpUrl


class ApplicationStatus(str, Enum):
    """Application status tracking."""
    PENDING = "pending"
    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    PHONE_SCREEN = "phone_screen"
    TECHNICAL_INTERVIEW = "technical_interview"
    ONSITE_INTERVIEW = "onsite_interview"
    FINAL_INTERVIEW = "final_interview"
    OFFER_RECEIVED = "offer_received"
    OFFER_ACCEPTED = "offer_accepted"
    OFFER_DECLINED = "offer_declined"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"
    NO_RESPONSE = "no_response"


class ApplicationMethod(str, Enum):
    """Method used to apply for the job."""
    EASY_APPLY = "easy_apply"
    COMPANY_WEBSITE = "company_website"
    EMAIL = "email"
    RECRUITER_CONTACT = "recruiter_contact"
    REFERRAL = "referral"
    OTHER = "other"


class InterviewType(str, Enum):
    """Types of interviews."""
    PHONE = "phone"
    VIDEO = "video"
    IN_PERSON = "in_person"
    TECHNICAL = "technical"
    BEHAVIORAL = "behavioral"
    PANEL = "panel"
    PRESENTATION = "presentation"


class ApplicationResponse(BaseModel):
    """Response or communication from employer."""
    response_id: str = Field(..., min_length=1)
    response_date: datetime = Field(default_factory=datetime.utcnow)
    response_type: str = Field(..., min_length=1)  # email, phone, message, etc.
    sender: Optional[str] = None  # recruiter name, HR, etc.
    subject: Optional[str] = None
    content: str = Field(..., min_length=1)
    
    # Interview scheduling
    interview_scheduled: bool = Field(False)
    interview_date: Optional[datetime] = None
    interview_type: Optional[InterviewType] = None
    interview_location: Optional[str] = None
    interview_notes: Optional[str] = None
    
    # Action items
    requires_response: bool = Field(False)
    response_deadline: Optional[datetime] = None
    action_items: List[str] = Field(default=[])
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class Application(BaseModel):
    """Complete application tracking model."""
    # Unique identifiers
    application_id: str = Field(..., min_length=1)
    job_id: str = Field(..., min_length=1)  # Reference to JobPosting
    user_id: str = Field(..., min_length=1)  # Reference to UserProfile
    
    # Application details
    status: ApplicationStatus = Field(ApplicationStatus.PENDING)
    application_method: ApplicationMethod
    application_date: datetime = Field(default_factory=datetime.utcnow)
    
    # Job information (cached for quick access)
    job_title: str = Field(..., min_length=1)
    company_name: str = Field(..., min_length=1)
    job_url: HttpUrl
    
    # Application materials used
    resume_version: Optional[str] = None
    cover_letter_used: bool = Field(False)
    cover_letter_content: Optional[str] = None
    
    # Application questions and responses
    application_questions: Dict[str, str] = Field(default={})
    custom_responses: Dict[str, str] = Field(default={})
    
    # Tracking and follow-up
    responses: List[ApplicationResponse] = Field(default=[])
    last_contact_date: Optional[datetime] = None
    next_follow_up_date: Optional[datetime] = None
    
    # Interview tracking
    interviews_scheduled: int = Field(0, ge=0)
    interviews_completed: int = Field(0, ge=0)
    current_interview_stage: Optional[str] = None
    
    # Outcome tracking
    rejection_reason: Optional[str] = None
    offer_details: Optional[Dict[str, Any]] = None
    salary_offered: Optional[float] = None
    
    # Automation metadata
    automated_application: bool = Field(True)
    automation_errors: List[str] = Field(default=[])
    manual_intervention_required: bool = Field(False)
    
    # Notion integration
    notion_page_id: Optional[str] = None
    notion_url: Optional[HttpUrl] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
    
    def update_timestamp(self) -> None:
        """Update the last modified timestamp."""
        self.updated_at = datetime.utcnow()
    
    def update_status(self, new_status: ApplicationStatus, notes: Optional[str] = None) -> None:
        """Update application status."""
        self.status = new_status
        self.update_timestamp()
        
        if notes:
            response = ApplicationResponse(
                response_id=f"status_update_{datetime.utcnow().isoformat()}",
                response_type="status_update",
                content=notes
            )
            self.add_response(response)
    
    def add_response(self, response: ApplicationResponse) -> None:
        """Add a new response/communication."""
        self.responses.append(response)
        self.last_contact_date = response.response_date
        self.update_timestamp()
        
        # Auto-update status based on response content
        content_lower = response.content.lower()
        if any(word in content_lower for word in ["interview", "schedule", "meet"]):
            if self.status == ApplicationStatus.SUBMITTED:
                self.status = ApplicationStatus.UNDER_REVIEW
        elif any(word in content_lower for word in ["reject", "unfortunately", "not moving forward"]):
            self.status = ApplicationStatus.REJECTED
        elif any(word in content_lower for word in ["offer", "congratulations", "pleased to offer"]):
            self.status = ApplicationStatus.OFFER_RECEIVED
    
    def schedule_interview(self, interview_date: datetime, interview_type: InterviewType, 
                          location: Optional[str] = None, notes: Optional[str] = None) -> None:
        """Schedule an interview."""
        self.interviews_scheduled += 1
        self.current_interview_stage = interview_type.value
        
        # Update status based on interview type
        if interview_type in [InterviewType.PHONE, InterviewType.VIDEO]:
            self.status = ApplicationStatus.PHONE_SCREEN
        elif interview_type == InterviewType.TECHNICAL:
            self.status = ApplicationStatus.TECHNICAL_INTERVIEW
        else:
            self.status = ApplicationStatus.ONSITE_INTERVIEW
        
        # Create response record
        response = ApplicationResponse(
            response_id=f"interview_{datetime.utcnow().isoformat()}",
            response_type="interview_invitation",
            content=f"Interview scheduled for {interview_date.isoformat()}",
            interview_scheduled=True,
            interview_date=interview_date,
            interview_type=interview_type,
            interview_location=location,
            interview_notes=notes
        )
        self.add_response(response)
    
    def complete_interview(self, notes: Optional[str] = None) -> None:
        """Mark interview as completed."""
        self.interviews_completed += 1
        self.update_timestamp()
        
        if notes:
            response = ApplicationResponse(
                response_id=f"interview_complete_{datetime.utcnow().isoformat()}",
                response_type="interview_feedback",
                content=notes
            )
            self.add_response(response)
    
    def add_automation_error(self, error: str) -> None:
        """Add an automation error."""
        self.automation_errors.append(f"{datetime.utcnow().isoformat()}: {error}")
        self.manual_intervention_required = True
        self.update_timestamp()
    
    def get_days_since_application(self) -> int:
        """Get number of days since application was submitted."""
        return (datetime.utcnow() - self.application_date).days
    
    def get_last_activity_days(self) -> int:
        """Get number of days since last activity."""
        last_activity = self.last_contact_date or self.application_date
        return (datetime.utcnow() - last_activity).days
    
    def needs_follow_up(self) -> bool:
        """Check if application needs follow-up."""
        if self.status in [ApplicationStatus.REJECTED, ApplicationStatus.OFFER_ACCEPTED, 
                          ApplicationStatus.WITHDRAWN]:
            return False
        
        days_since_last_activity = self.get_last_activity_days()
        
        # Follow-up rules
        if self.status == ApplicationStatus.SUBMITTED and days_since_last_activity > 7:
            return True
        elif self.status in [ApplicationStatus.UNDER_REVIEW, ApplicationStatus.PHONE_SCREEN] and days_since_last_activity > 14:
            return True
        elif self.status in [ApplicationStatus.TECHNICAL_INTERVIEW, ApplicationStatus.ONSITE_INTERVIEW] and days_since_last_activity > 10:
            return True
        
        return False
    
    def to_notion_properties(self) -> Dict[str, Any]:
        """Convert to Notion database properties format."""
        return {
            "Job Title": {"title": [{"text": {"content": self.job_title}}]},
            "Company": {"rich_text": [{"text": {"content": self.company_name}}]},
            "Status": {"select": {"name": self.status.value.replace("_", " ").title()}},
            "Application Date": {"date": {"start": self.application_date.isoformat()}},
            "Job URL": {"url": str(self.job_url)},
            "Method": {"select": {"name": self.application_method.value.replace("_", " ").title()}},
            "Days Since Applied": {"number": self.get_days_since_application()},
            "Interviews": {"number": self.interviews_completed},
            "Automated": {"checkbox": self.automated_application},
            "Needs Follow-up": {"checkbox": self.needs_follow_up()},
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Application":
        """Create instance from dictionary."""
        return cls(**data)
