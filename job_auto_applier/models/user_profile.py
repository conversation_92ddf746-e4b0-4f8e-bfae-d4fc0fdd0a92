"""User profile and preferences data models."""

from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, EmailStr, validator
from pathlib import Path


class ExperienceLevel(str, Enum):
    """Experience level options."""
    INTERNSHIP = "internship"
    ENTRY_LEVEL = "entry_level"
    ASSOCIATE = "associate"
    MID_LEVEL = "mid_level"
    SENIOR = "senior"
    LEAD = "lead"
    PRINCIPAL = "principal"


class CompanySize(str, Enum):
    """Company size preferences."""
    STARTUP = "startup"
    SMALL = "small"
    MID_SIZE = "mid_size"
    LARGE = "large"
    ENTERPRISE = "enterprise"


class WorkType(str, Enum):
    """Work type preferences."""
    REMOTE = "remote"
    HYBRID = "hybrid"
    ON_SITE = "on_site"


class PersonalInfo(BaseModel):
    """Personal information model."""
    first_name: str = <PERSON>(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    email: EmailStr
    phone: Optional[str] = Field(None, regex=r'^\+?[\d\s\-\(\)]+$')
    location: str = Field(..., min_length=1, max_length=100)
    linkedin_url: Optional[str] = Field(None, regex=r'^https://www\.linkedin\.com/in/.+')
    github_url: Optional[str] = Field(None, regex=r'^https://github\.com/.+')
    portfolio_url: Optional[str] = None
    
    @property
    def full_name(self) -> str:
        """Get full name."""
        return f"{self.first_name} {self.last_name}"


class JobPreferences(BaseModel):
    """Job search preferences model."""
    target_roles: List[str] = Field(..., min_items=1)
    experience_levels: List[ExperienceLevel] = Field(..., min_items=1)
    preferred_locations: List[str] = Field(..., min_items=1)
    work_types: List[WorkType] = Field(default=[WorkType.REMOTE])
    company_sizes: List[CompanySize] = Field(default=list(CompanySize))
    
    # Salary preferences
    min_salary: Optional[int] = Field(None, ge=0)
    max_salary: Optional[int] = Field(None, ge=0)
    currency: str = Field("USD", min_length=3, max_length=3)
    
    # Keywords and skills
    required_keywords: List[str] = Field(default=[])
    preferred_keywords: List[str] = Field(default=[])
    excluded_keywords: List[str] = Field(default=[])
    
    # Industry preferences
    preferred_industries: List[str] = Field(default=[])
    excluded_industries: List[str] = Field(default=[])
    
    # Application settings
    max_applications_per_day: int = Field(10, ge=1, le=50)
    auto_apply_easy_apply: bool = Field(True)
    auto_apply_external: bool = Field(False)
    
    @validator("max_salary")
    def validate_salary_range(cls, v, values):
        """Ensure max salary is greater than min salary."""
        if v is not None and "min_salary" in values and values["min_salary"] is not None:
            if v <= values["min_salary"]:
                raise ValueError("Max salary must be greater than min salary")
        return v


class ApplicationAnswers(BaseModel):
    """Standard application question answers."""
    # Common questions
    why_interested: str = Field(..., min_length=50, max_length=1000)
    why_good_fit: str = Field(..., min_length=50, max_length=1000)
    career_goals: str = Field(..., min_length=50, max_length=1000)
    
    # Experience questions
    relevant_experience: str = Field(..., min_length=50, max_length=1000)
    biggest_achievement: str = Field(..., min_length=50, max_length=1000)
    technical_skills: str = Field(..., min_length=50, max_length=1000)
    
    # Availability and logistics
    available_start_date: str = Field("2 weeks notice", max_length=100)
    willing_to_relocate: bool = Field(False)
    requires_sponsorship: bool = Field(False)
    
    # Custom answers for specific questions
    custom_answers: Dict[str, str] = Field(default={})
    
    def get_answer(self, question: str) -> Optional[str]:
        """Get answer for a specific question."""
        # Check custom answers first
        if question in self.custom_answers:
            return self.custom_answers[question]
        
        # Map common question patterns to standard answers
        question_lower = question.lower()
        
        if any(keyword in question_lower for keyword in ["why", "interested", "company"]):
            return self.why_interested
        elif any(keyword in question_lower for keyword in ["fit", "qualified", "suitable"]):
            return self.why_good_fit
        elif any(keyword in question_lower for keyword in ["goals", "future", "career"]):
            return self.career_goals
        elif any(keyword in question_lower for keyword in ["experience", "background"]):
            return self.relevant_experience
        elif any(keyword in question_lower for keyword in ["achievement", "accomplishment"]):
            return self.biggest_achievement
        elif any(keyword in question_lower for keyword in ["skills", "technical", "technology"]):
            return self.technical_skills
        elif any(keyword in question_lower for keyword in ["start", "available", "when"]):
            return self.available_start_date
        elif any(keyword in question_lower for keyword in ["relocate", "move", "location"]):
            return "Yes" if self.willing_to_relocate else "No"
        elif any(keyword in question_lower for keyword in ["sponsor", "visa", "authorization"]):
            return "Yes" if self.requires_sponsorship else "No"
        
        return None


class UserProfile(BaseModel):
    """Complete user profile model."""
    user_id: str = Field(..., min_length=1)
    personal_info: PersonalInfo
    job_preferences: JobPreferences
    application_answers: ApplicationAnswers
    
    # Resume and documents
    resume_path: Optional[Path] = None
    cover_letter_template: Optional[str] = None
    
    # Account credentials (stored encrypted)
    linkedin_email: Optional[str] = None
    indeed_email: Optional[str] = None
    glassdoor_email: Optional[str] = None
    
    # Tracking and metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_job_search: Optional[datetime] = None
    total_applications: int = Field(0, ge=0)
    
    # Settings
    notifications_enabled: bool = Field(True)
    auto_apply_enabled: bool = Field(True)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Path: lambda v: str(v) if v else None,
        }
    
    def update_timestamp(self) -> None:
        """Update the last modified timestamp."""
        self.updated_at = datetime.utcnow()
    
    def increment_applications(self) -> None:
        """Increment the total applications counter."""
        self.total_applications += 1
        self.update_timestamp()
    
    def update_last_search(self) -> None:
        """Update the last job search timestamp."""
        self.last_job_search = datetime.utcnow()
        self.update_timestamp()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "UserProfile":
        """Create instance from dictionary."""
        return cls(**data)
