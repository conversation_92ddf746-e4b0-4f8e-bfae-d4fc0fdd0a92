"""
Job Auto Applier - Intelligent Job Application Automation

An intelligent, persistent job application agent that leverages the browser-use library
for fully automated job searching, application submission, and tracking.
"""

__version__ = "1.0.0"
__author__ = "Job Auto Applier Team"
__email__ = "<EMAIL>"

from .core.agent import JobApplicationAgent
from .config.settings import Settings
from .models.user_profile import UserProfile
from .models.job_posting import JobPosting
from .models.application import Application

__all__ = [
    "JobApplicationAgent",
    "Settings", 
    "UserProfile",
    "JobPosting",
    "Application",
]
