"""AI-powered question answering for job applications."""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain.schema import HumanMessage, SystemMessage

from ..config.settings import get_settings
from ..models.user_profile import UserProfile
from ..models.job_posting import JobPosting

logger = logging.getLogger(__name__)


class QuestionAnsweringEngine:
    """AI engine for answering job application questions."""
    
    def __init__(self, user_profile: UserProfile):
        """Initialize the question answering engine."""
        self.user_profile = user_profile
        self.settings = get_settings()
        self.llm = self._initialize_llm()
        
        # Question categories and patterns
        self.question_patterns = {
            "motivation": [
                "why", "interested", "motivation", "passion", "excited",
                "what attracts you", "what draws you", "why this company"
            ],
            "fit": [
                "good fit", "qualified", "suitable", "right candidate",
                "why should we hire", "what makes you", "strengths"
            ],
            "experience": [
                "experience", "background", "previous", "worked on",
                "projects", "accomplishments", "achievements"
            ],
            "technical": [
                "technical", "skills", "programming", "languages",
                "frameworks", "tools", "technologies"
            ],
            "career_goals": [
                "goals", "future", "career", "aspirations", "where do you see",
                "long term", "5 years", "growth"
            ],
            "availability": [
                "start", "available", "when can you", "notice period",
                "earliest", "begin"
            ],
            "logistics": [
                "relocate", "move", "location", "remote", "travel",
                "sponsorship", "visa", "authorization"
            ],
            "salary": [
                "salary", "compensation", "pay", "expected", "range",
                "requirements", "benefits"
            ],
            "company_specific": [
                "company", "organization", "culture", "values",
                "mission", "vision", "team"
            ]
        }
    
    def _initialize_llm(self):
        """Initialize the appropriate LLM."""
        provider = self.settings.get_ai_provider()
        
        if provider == "openai":
            return ChatOpenAI(
                model="gpt-4o",
                api_key=self.settings.openai_api_key,
                temperature=0.3  # Slightly more creative for personalized responses
            )
        elif provider == "anthropic":
            return ChatAnthropic(
                model="claude-3-sonnet-20240229",
                api_key=self.settings.anthropic_api_key,
                temperature=0.3
            )
        else:
            raise ValueError(f"Unsupported AI provider: {provider}")
    
    def categorize_question(self, question: str) -> str:
        """Categorize a question based on its content."""
        question_lower = question.lower()
        
        # Count matches for each category
        category_scores = {}
        for category, patterns in self.question_patterns.items():
            score = sum(1 for pattern in patterns if pattern in question_lower)
            if score > 0:
                category_scores[category] = score
        
        # Return the category with the highest score
        if category_scores:
            return max(category_scores, key=category_scores.get)
        else:
            return "general"
    
    async def answer_question(self, question: str, job_posting: Optional[JobPosting] = None,
                            context: Optional[Dict[str, Any]] = None) -> str:
        """Generate an answer to a job application question."""
        try:
            # First check if we have a pre-defined answer
            predefined_answer = self.user_profile.application_answers.get_answer(question)
            if predefined_answer:
                # Customize the predefined answer for this specific job
                return await self._customize_answer(predefined_answer, question, job_posting)
            
            # Generate a new answer using AI
            return await self._generate_ai_answer(question, job_posting, context)
            
        except Exception as e:
            logger.error(f"Failed to answer question '{question}': {e}")
            return self._get_fallback_answer(question)
    
    async def _customize_answer(self, base_answer: str, question: str, 
                              job_posting: Optional[JobPosting] = None) -> str:
        """Customize a predefined answer for a specific job."""
        try:
            system_prompt = """
            You are helping customize a job application answer for a specific position.
            Take the base answer and make it more specific and relevant to the job posting.
            Keep the core message but add relevant details about the company, role, or industry.
            Maintain a professional tone and keep the answer concise (2-3 sentences).
            """
            
            job_context = ""
            if job_posting:
                job_context = f"""
                Job Title: {job_posting.title}
                Company: {job_posting.company.name}
                Industry: {job_posting.company.industry or 'Technology'}
                Job Description: {job_posting.description[:500]}...
                """
            
            user_prompt = f"""
            Question: {question}
            Base Answer: {base_answer}
            
            Job Context:
            {job_context}
            
            Please customize this answer to be more specific to this job opportunity.
            """
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to customize answer: {e}")
            return base_answer
    
    async def _generate_ai_answer(self, question: str, job_posting: Optional[JobPosting] = None,
                                context: Optional[Dict[str, Any]] = None) -> str:
        """Generate a new answer using AI."""
        try:
            category = self.categorize_question(question)
            
            system_prompt = f"""
            You are an expert at writing job application responses. Generate a professional,
            concise, and compelling answer to the job application question.
            
            User Profile:
            - Name: {self.user_profile.personal_info.full_name}
            - Background: {self.user_profile.application_answers.relevant_experience}
            - Skills: {self.user_profile.application_answers.technical_skills}
            - Career Goals: {self.user_profile.application_answers.career_goals}
            
            Guidelines:
            - Keep answers to 2-3 sentences unless more detail is specifically requested
            - Be specific and avoid generic responses
            - Show enthusiasm and knowledge about the role/company
            - Highlight relevant experience and skills
            - Use a professional but personable tone
            - Category: {category}
            """
            
            job_context = ""
            if job_posting:
                job_context = f"""
                Job Details:
                - Title: {job_posting.title}
                - Company: {job_posting.company.name}
                - Industry: {job_posting.company.industry or 'Technology'}
                - Location: {job_posting.location}
                - Key Requirements: {', '.join(job_posting.requirements.required_skills[:5])}
                - Description: {job_posting.description[:300]}...
                """
            
            additional_context = ""
            if context:
                additional_context = f"Additional Context: {context}"
            
            user_prompt = f"""
            Question: {question}
            
            {job_context}
            {additional_context}
            
            Please provide a tailored, professional response to this question.
            """
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            answer = response.content.strip()
            
            # Log the generated answer for review
            logger.info(f"Generated AI answer for question: {question[:50]}...")
            
            return answer
            
        except Exception as e:
            logger.error(f"Failed to generate AI answer: {e}")
            return self._get_fallback_answer(question)
    
    def _get_fallback_answer(self, question: str) -> str:
        """Get a fallback answer when AI generation fails."""
        category = self.categorize_question(question)
        
        fallback_answers = {
            "motivation": "I am excited about this opportunity because it aligns perfectly with my career goals and allows me to contribute my skills while growing professionally.",
            "fit": "My background in software development and passion for technology make me well-suited for this role, and I'm eager to bring my skills to your team.",
            "experience": "I have relevant experience in software development with a focus on modern technologies and best practices.",
            "technical": "I have strong technical skills in programming languages and frameworks relevant to this position.",
            "career_goals": "I aim to grow as a software engineer while contributing to meaningful projects and collaborating with talented teams.",
            "availability": "I am available to start within two weeks' notice and am flexible with timing.",
            "logistics": f"{'Yes' if self.user_profile.application_answers.willing_to_relocate else 'No'}, I am {'willing' if self.user_profile.application_answers.willing_to_relocate else 'not able'} to relocate for this position.",
            "salary": "I am open to discussing compensation based on the role's responsibilities and market standards.",
            "company_specific": "I am impressed by your company's reputation and would be excited to contribute to your team's success.",
            "general": "Thank you for this question. I believe my background and enthusiasm make me a strong candidate for this position."
        }
        
        return fallback_answers.get(category, fallback_answers["general"])
    
    async def batch_answer_questions(self, questions: List[str], 
                                   job_posting: Optional[JobPosting] = None) -> Dict[str, str]:
        """Answer multiple questions efficiently."""
        answers = {}
        
        for question in questions:
            try:
                answer = await self.answer_question(question, job_posting)
                answers[question] = answer
            except Exception as e:
                logger.error(f"Failed to answer question '{question}': {e}")
                answers[question] = self._get_fallback_answer(question)
        
        return answers
    
    def validate_answer_length(self, answer: str, max_length: int = 500) -> str:
        """Validate and truncate answer if necessary."""
        if len(answer) <= max_length:
            return answer
        
        # Truncate at the last complete sentence within the limit
        truncated = answer[:max_length]
        last_period = truncated.rfind('.')
        
        if last_period > max_length * 0.7:  # If we can keep most of the content
            return truncated[:last_period + 1]
        else:
            return truncated.rstrip() + "..."
    
    async def improve_answer(self, question: str, current_answer: str, 
                           feedback: str) -> str:
        """Improve an answer based on feedback."""
        try:
            system_prompt = """
            You are helping improve a job application answer based on feedback.
            Revise the answer to address the feedback while maintaining professionalism.
            """
            
            user_prompt = f"""
            Question: {question}
            Current Answer: {current_answer}
            Feedback: {feedback}
            
            Please provide an improved version of the answer.
            """
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to improve answer: {e}")
            return current_answer
