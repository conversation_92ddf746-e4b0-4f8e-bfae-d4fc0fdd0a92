"""Encryption and security utilities for sensitive data."""

import os
import base64
import keyring
from typing import Optional, Union, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import json
import logging

logger = logging.getLogger(__name__)


class EncryptionManager:
    """Manages encryption and decryption of sensitive data."""
    
    SERVICE_NAME = "job_auto_applier"
    
    def __init__(self, master_password: Optional[str] = None):
        """Initialize encryption manager with optional master password."""
        self.master_password = master_password
        self._fernet: Optional[Fernet] = None
        
    def _get_master_password(self) -> str:
        """Get or prompt for master password."""
        if self.master_password:
            return self.master_password
            
        # Try to get from keyring first
        stored_password = keyring.get_password(self.SERVICE_NAME, "master_password")
        if stored_password:
            return stored_password
            
        # Prompt user for password
        import getpass
        password = getpass.getpass("Enter master password for encryption: ")
        
        # Store in keyring for future use
        keyring.set_password(self.SERVICE_NAME, "master_password", password)
        return password
    
    def _get_encryption_key(self) -> bytes:
        """Derive encryption key from master password."""
        password = self._get_master_password().encode()
        salt = b"job_auto_applier_salt"  # In production, use random salt
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    def _get_fernet(self) -> Fernet:
        """Get or create Fernet instance for encryption."""
        if self._fernet is None:
            key = self._get_encryption_key()
            self._fernet = Fernet(key)
        return self._fernet
    
    def encrypt(self, data: Union[str, Dict[str, Any]]) -> str:
        """Encrypt data and return base64 encoded string."""
        try:
            if isinstance(data, dict):
                data = json.dumps(data)
            
            encrypted_data = self._get_fernet().encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt base64 encoded encrypted data."""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._get_fernet().decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise
    
    def decrypt_json(self, encrypted_data: str) -> Dict[str, Any]:
        """Decrypt and parse JSON data."""
        decrypted_str = self.decrypt(encrypted_data)
        return json.loads(decrypted_str)
    
    def store_credential(self, username: str, credential_type: str, value: str) -> None:
        """Store encrypted credential in system keyring."""
        try:
            encrypted_value = self.encrypt(value)
            keyring.set_password(
                self.SERVICE_NAME, 
                f"{username}_{credential_type}", 
                encrypted_value
            )
            logger.info(f"Stored credential for {username}:{credential_type}")
        except Exception as e:
            logger.error(f"Failed to store credential: {e}")
            raise
    
    def get_credential(self, username: str, credential_type: str) -> Optional[str]:
        """Retrieve and decrypt credential from system keyring."""
        try:
            encrypted_value = keyring.get_password(
                self.SERVICE_NAME, 
                f"{username}_{credential_type}"
            )
            if encrypted_value:
                return self.decrypt(encrypted_value)
            return None
        except Exception as e:
            logger.error(f"Failed to retrieve credential: {e}")
            return None
    
    def delete_credential(self, username: str, credential_type: str) -> None:
        """Delete credential from system keyring."""
        try:
            keyring.delete_password(
                self.SERVICE_NAME, 
                f"{username}_{credential_type}"
            )
            logger.info(f"Deleted credential for {username}:{credential_type}")
        except Exception as e:
            logger.warning(f"Failed to delete credential: {e}")
    
    def store_user_data(self, user_id: str, data: Dict[str, Any]) -> None:
        """Store encrypted user data in keyring."""
        try:
            encrypted_data = self.encrypt(data)
            keyring.set_password(self.SERVICE_NAME, f"user_data_{user_id}", encrypted_data)
            logger.info(f"Stored user data for {user_id}")
        except Exception as e:
            logger.error(f"Failed to store user data: {e}")
            raise
    
    def get_user_data(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve and decrypt user data from keyring."""
        try:
            encrypted_data = keyring.get_password(self.SERVICE_NAME, f"user_data_{user_id}")
            if encrypted_data:
                return self.decrypt_json(encrypted_data)
            return None
        except Exception as e:
            logger.error(f"Failed to retrieve user data: {e}")
            return None
    
    def list_stored_credentials(self) -> list[str]:
        """List all stored credential keys (for debugging/management)."""
        # Note: keyring doesn't provide a direct way to list all keys
        # This is a placeholder for potential future implementation
        logger.warning("Credential listing not implemented for security reasons")
        return []
    
    @staticmethod
    def generate_key() -> str:
        """Generate a new encryption key."""
        return Fernet.generate_key().decode()
    
    def verify_master_password(self, password: str) -> bool:
        """Verify if the provided password matches the stored master password."""
        try:
            stored_password = keyring.get_password(self.SERVICE_NAME, "master_password")
            return stored_password == password
        except Exception as e:
            logger.error(f"Password verification failed: {e}")
            return False
    
    def change_master_password(self, old_password: str, new_password: str) -> bool:
        """Change the master password and re-encrypt all data."""
        try:
            if not self.verify_master_password(old_password):
                logger.error("Old password verification failed")
                return False
            
            # Store new password
            keyring.set_password(self.SERVICE_NAME, "master_password", new_password)
            
            # Note: In a full implementation, you would need to:
            # 1. Decrypt all stored data with old password
            # 2. Re-encrypt with new password
            # 3. Update all stored credentials
            
            logger.info("Master password changed successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to change master password: {e}")
            return False
