"""Application settings and configuration management."""

import os
from pathlib import Path
from typing import Optional


class Settings:
    """Application settings with environment variable support."""

    def __init__(self):
        """Initialize settings from environment variables."""
        # Load .env file if it exists
        self._load_env_file()

        # API Keys
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        self.azure_openai_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self.azure_openai_key = os.getenv("AZURE_OPENAI_KEY")
        self.google_api_key = os.getenv("GOOGLE_API_KEY")

        # Notion Integration
        self.notion_api_key = os.getenv("NOTION_API_KEY")
        self.notion_database_id = os.getenv("NOTION_DATABASE_ID")

        # Database Configuration
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///job_applications.db")
        self.database_encryption_key = os.getenv("DATABASE_ENCRYPTION_KEY")

        # Security Settings
        self.master_password = os.getenv("MASTER_PASSWORD")
        self.secret_key = os.getenv("SECRET_KEY")

        # Browser Settings
        self.headless_mode = os.getenv("HEADLESS_MODE", "true").lower() == "true"
        self.browser_timeout = int(os.getenv("BROWSER_TIMEOUT", "30000"))
        self.screenshot_on_error = os.getenv("SCREENSHOT_ON_ERROR", "true").lower() == "true"

        # Job Search Settings
        self.max_applications_per_day = int(os.getenv("MAX_APPLICATIONS_PER_DAY", "10"))
        self.search_delay_seconds = int(os.getenv("SEARCH_DELAY_SECONDS", "5"))
        self.application_delay_seconds = int(os.getenv("APPLICATION_DELAY_SECONDS", "10"))

        # Logging Configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        self.log_file = os.getenv("LOG_FILE", "logs/job_applier.log")

        # Notification Settings
        self.slack_webhook_url = os.getenv("SLACK_WEBHOOK_URL")
        self.email_smtp_server = os.getenv("EMAIL_SMTP_SERVER")
        self.email_smtp_port = int(os.getenv("EMAIL_SMTP_PORT", "587"))
        self.email_username = os.getenv("EMAIL_USERNAME")
        self.email_password = os.getenv("EMAIL_PASSWORD")

        # Development Settings
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        self.development_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"

        # Application Directories
        self.data_dir = Path.home() / ".job_auto_applier"
        self.logs_dir = self.data_dir / "logs"
        self.cache_dir = self.data_dir / "cache"

        # Create directories
        self._create_directories()

    def _load_env_file(self):
        """Load environment variables from .env file."""
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file) as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#") and "=" in line:
                        key, value = line.split("=", 1)
                        os.environ.setdefault(key.strip(), value.strip())

    def _create_directories(self):
        """Create necessary directories."""
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)

    def get_ai_provider(self) -> str:
        """Determine which AI provider to use based on available keys."""
        if self.openai_api_key:
            return "openai"
        elif self.anthropic_api_key:
            return "anthropic"
        elif self.azure_openai_key and self.azure_openai_endpoint:
            return "azure"
        elif self.google_api_key:
            return "google"
        else:
            raise ValueError("No AI provider API key configured")

    def has_notion_integration(self) -> bool:
        """Check if Notion integration is configured."""
        return bool(self.notion_api_key and self.notion_database_id)

    def has_notifications_configured(self) -> bool:
        """Check if any notification method is configured."""
        return bool(
            self.slack_webhook_url or
            (self.email_smtp_server and self.email_username and self.email_password)
        )


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment and files."""
    global settings
    settings = Settings()
    return settings
