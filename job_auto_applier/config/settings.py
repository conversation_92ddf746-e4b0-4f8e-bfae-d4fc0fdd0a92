"""Application settings and configuration management."""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any
from pydantic import BaseSettings, Field, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings


class Settings(PydanticBaseSettings):
    """Application settings with environment variable support."""
    
    # API Keys
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    azure_openai_endpoint: Optional[str] = Field(None, env="AZURE_OPENAI_ENDPOINT")
    azure_openai_key: Optional[str] = Field(None, env="AZURE_OPENAI_KEY")
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    
    # Notion Integration
    notion_api_key: Optional[str] = Field(None, env="NOTION_API_KEY")
    notion_database_id: Optional[str] = Field(None, env="NOTION_DATABASE_ID")
    
    # Database Configuration
    database_url: str = Field("sqlite:///job_applications.db", env="DATABASE_URL")
    database_encryption_key: Optional[str] = Field(None, env="DATABASE_ENCRYPTION_KEY")
    
    # Security Settings
    master_password: Optional[str] = Field(None, env="MASTER_PASSWORD")
    secret_key: Optional[str] = Field(None, env="SECRET_KEY")
    
    # Browser Settings
    headless_mode: bool = Field(True, env="HEADLESS_MODE")
    browser_timeout: int = Field(30000, env="BROWSER_TIMEOUT")
    screenshot_on_error: bool = Field(True, env="SCREENSHOT_ON_ERROR")
    
    # Job Search Settings
    max_applications_per_day: int = Field(10, env="MAX_APPLICATIONS_PER_DAY")
    search_delay_seconds: int = Field(5, env="SEARCH_DELAY_SECONDS")
    application_delay_seconds: int = Field(10, env="APPLICATION_DELAY_SECONDS")
    
    # Logging Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("logs/job_applier.log", env="LOG_FILE")
    
    # Notification Settings
    slack_webhook_url: Optional[str] = Field(None, env="SLACK_WEBHOOK_URL")
    email_smtp_server: Optional[str] = Field(None, env="EMAIL_SMTP_SERVER")
    email_smtp_port: int = Field(587, env="EMAIL_SMTP_PORT")
    email_username: Optional[str] = Field(None, env="EMAIL_USERNAME")
    email_password: Optional[str] = Field(None, env="EMAIL_PASSWORD")
    
    # Development Settings
    debug: bool = Field(False, env="DEBUG")
    development_mode: bool = Field(False, env="DEVELOPMENT_MODE")
    
    # Application Directories
    data_dir: Path = Field(default_factory=lambda: Path.home() / ".job_auto_applier")
    logs_dir: Path = Field(default_factory=lambda: Path.home() / ".job_auto_applier" / "logs")
    cache_dir: Path = Field(default_factory=lambda: Path.home() / ".job_auto_applier" / "cache")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    @validator("data_dir", "logs_dir", "cache_dir", pre=True, always=True)
    def create_directories(cls, v):
        """Ensure directories exist."""
        if isinstance(v, str):
            v = Path(v)
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()
    
    def get_ai_provider(self) -> str:
        """Determine which AI provider to use based on available keys."""
        if self.openai_api_key:
            return "openai"
        elif self.anthropic_api_key:
            return "anthropic"
        elif self.azure_openai_key and self.azure_openai_endpoint:
            return "azure"
        elif self.google_api_key:
            return "google"
        else:
            raise ValueError("No AI provider API key configured")
    
    def has_notion_integration(self) -> bool:
        """Check if Notion integration is configured."""
        return bool(self.notion_api_key and self.notion_database_id)
    
    def has_notifications_configured(self) -> bool:
        """Check if any notification method is configured."""
        return bool(
            self.slack_webhook_url or 
            (self.email_smtp_server and self.email_username and self.email_password)
        )


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment and files."""
    global settings
    settings = Settings()
    return settings
