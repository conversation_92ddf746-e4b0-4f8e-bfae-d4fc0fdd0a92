"""Main job application agent orchestrator."""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..config.settings import get_settings
from ..models.user_profile import UserProfile
from ..models.job_posting import JobPosting, JobSource
from ..models.application import Application
from ..models.database import get_database_manager
from ..automation.linkedin_automation import LinkedInAutomation
from ..automation.indeed_automation import IndeedAutomation
from ..automation.glassdoor_automation import GlassdoorA<PERSON><PERSON>
from ..ai.question_answering import QuestionAnsweringEngine
from ..tracking.notion_tracker import NotionTracker

logger = logging.getLogger(__name__)


class JobApplicationAgent:
    """Main agent for automated job applications."""
    
    def __init__(self, user_profile: UserProfile):
        """Initialize the job application agent."""
        self.user_profile = user_profile
        self.settings = get_settings()
        self.db_manager = get_database_manager()
        
        # Initialize AI components
        self.question_engine = QuestionAnsweringEngine(user_profile)
        
        # Initialize tracking
        self.notion_tracker = None
        if self.settings.has_notion_integration():
            self.notion_tracker = NotionTracker()
        
        # Initialize automation modules
        self.automations = {
            JobSource.LINKEDIN: LinkedInAutomation(user_profile),
            # JobSource.INDEED: IndeedAutomation(user_profile),
            # JobSource.GLASSDOOR: GlassdoorAutomation(user_profile),
        }
        
        # Daily limits and tracking
        self.daily_application_count = 0
        self.max_daily_applications = user_profile.job_preferences.max_applications_per_day
        self.applied_jobs_today = set()
        
    async def initialize(self) -> bool:
        """Initialize the agent and all components."""
        try:
            logger.info("Initializing Job Application Agent...")
            
            # Initialize Notion tracker if configured
            if self.notion_tracker:
                await self.notion_tracker.initialize_database()
            
            # Load today's application count
            await self._load_daily_stats()
            
            logger.info("Job Application Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize agent: {e}")
            return False
    
    async def run_daily_job_search(self) -> Dict[str, Any]:
        """Run the daily job search and application process."""
        logger.info("Starting daily job search...")
        
        results = {
            "jobs_found": 0,
            "applications_submitted": 0,
            "errors": [],
            "summary": {}
        }
        
        try:
            # Check if we've reached daily limit
            if self.daily_application_count >= self.max_daily_applications:
                logger.info(f"Daily application limit reached ({self.max_daily_applications})")
                results["summary"]["limit_reached"] = True
                return results
            
            # Search for jobs on each platform
            all_jobs = []
            for source, automation in self.automations.items():
                try:
                    jobs = await self._search_jobs_on_platform(automation, source)
                    all_jobs.extend(jobs)
                    logger.info(f"Found {len(jobs)} jobs on {source.value}")
                except Exception as e:
                    error_msg = f"Error searching {source.value}: {e}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)
            
            results["jobs_found"] = len(all_jobs)
            
            # Filter and rank jobs
            filtered_jobs = await self._filter_and_rank_jobs(all_jobs)
            logger.info(f"Filtered to {len(filtered_jobs)} relevant jobs")
            
            # Apply to jobs
            applications = await self._apply_to_jobs(filtered_jobs)
            results["applications_submitted"] = len([app for app in applications if app.status == "submitted"])
            
            # Update tracking
            await self._update_tracking(applications)
            
            # Generate summary
            results["summary"] = await self._generate_daily_summary(applications)
            
            logger.info(f"Daily job search completed: {results['applications_submitted']} applications submitted")
            return results
            
        except Exception as e:
            error_msg = f"Daily job search failed: {e}"
            logger.error(error_msg)
            results["errors"].append(error_msg)
            return results
    
    async def _search_jobs_on_platform(self, automation, source: JobSource) -> List[JobPosting]:
        """Search for jobs on a specific platform."""
        try:
            async with automation:
                # Build search parameters
                keywords = self.user_profile.job_preferences.target_roles
                locations = self.user_profile.job_preferences.preferred_locations
                
                # Search on each location
                all_jobs = []
                for location in locations[:3]:  # Limit to top 3 locations
                    try:
                        jobs = await automation.search_jobs(
                            keywords=keywords,
                            location=location,
                            filters={
                                "remote_only": "remote" in location.lower(),
                                "easy_apply_only": self.user_profile.job_preferences.auto_apply_easy_apply
                            }
                        )
                        all_jobs.extend(jobs)
                        
                        # Add delay between searches
                        await asyncio.sleep(self.settings.search_delay_seconds)
                        
                    except Exception as e:
                        logger.error(f"Error searching {location} on {source.value}: {e}")
                
                return all_jobs
                
        except Exception as e:
            logger.error(f"Platform search failed for {source.value}: {e}")
            return []
    
    async def _filter_and_rank_jobs(self, jobs: List[JobPosting]) -> List[JobPosting]:
        """Filter and rank jobs based on user preferences."""
        filtered_jobs = []
        
        for job in jobs:
            try:
                # Skip if already applied
                if job.job_id in self.applied_jobs_today:
                    continue
                
                # Calculate match score
                match_score = job.calculate_match_score(self.user_profile.job_preferences)
                
                # Filter by minimum match score
                if match_score >= 0.3:  # 30% minimum match
                    filtered_jobs.append(job)
                    
                    # Save job to database
                    self.db_manager.save_job_posting(job)
                
            except Exception as e:
                logger.error(f"Error filtering job {job.job_id}: {e}")
        
        # Sort by match score (highest first)
        filtered_jobs.sort(key=lambda x: x.match_score or 0, reverse=True)
        
        # Limit to prevent overwhelming
        return filtered_jobs[:20]
    
    async def _apply_to_jobs(self, jobs: List[JobPosting]) -> List[Application]:
        """Apply to filtered jobs."""
        applications = []
        remaining_applications = self.max_daily_applications - self.daily_application_count
        
        for job in jobs[:remaining_applications]:
            try:
                # Get the appropriate automation
                automation = self.automations.get(job.source)
                if not automation:
                    logger.warning(f"No automation available for {job.source.value}")
                    continue
                
                # Apply to the job
                async with automation:
                    application = await automation.apply_to_job(job)
                    applications.append(application)
                    
                    # Update counters
                    if application.status == "submitted":
                        self.daily_application_count += 1
                        self.applied_jobs_today.add(job.job_id)
                        self.user_profile.increment_applications()
                    
                    # Save application to database
                    self.db_manager.save_application(application)
                    
                    # Add delay between applications
                    await asyncio.sleep(self.settings.application_delay_seconds)
                
                logger.info(f"Applied to {job.title} at {job.company.name}")
                
            except Exception as e:
                logger.error(f"Failed to apply to {job.title}: {e}")
                
                # Create failed application record
                from uuid import uuid4
                failed_app = Application(
                    application_id=str(uuid4()),
                    job_id=job.job_id,
                    user_id=self.user_profile.user_id,
                    status="failed",
                    application_method="automated",
                    job_title=job.title,
                    company_name=job.company.name,
                    job_url=job.url,
                    automated_application=True
                )
                failed_app.add_automation_error(str(e))
                applications.append(failed_app)
                self.db_manager.save_application(failed_app)
        
        return applications
    
    async def _update_tracking(self, applications: List[Application]) -> None:
        """Update tracking systems with new applications."""
        if not self.notion_tracker:
            return
        
        for application in applications:
            try:
                # Create Notion page for the application
                page_id = await self.notion_tracker.create_application_page(application)
                if page_id:
                    application.notion_page_id = page_id
                    # Update database with Notion page ID
                    self.db_manager.save_application(application)
                    
            except Exception as e:
                logger.error(f"Failed to update tracking for {application.application_id}: {e}")
    
    async def _generate_daily_summary(self, applications: List[Application]) -> Dict[str, Any]:
        """Generate a summary of the day's activities."""
        successful_apps = [app for app in applications if app.status == "submitted"]
        failed_apps = [app for app in applications if app.status == "failed"]
        
        summary = {
            "date": datetime.utcnow().isoformat(),
            "total_applications": len(applications),
            "successful_applications": len(successful_apps),
            "failed_applications": len(failed_apps),
            "daily_limit_reached": self.daily_application_count >= self.max_daily_applications,
            "companies_applied": list(set(app.company_name for app in successful_apps)),
            "top_matches": [
                {
                    "title": app.job_title,
                    "company": app.company_name,
                    "status": app.status
                }
                for app in applications[:5]
            ]
        }
        
        return summary
    
    async def _load_daily_stats(self) -> None:
        """Load today's application statistics."""
        try:
            today = datetime.utcnow().date()
            applications = self.db_manager.get_applications_by_user(self.user_profile.user_id)
            
            # Count today's applications
            today_apps = [
                app for app in applications 
                if app.application_date and app.application_date.date() == today
            ]
            
            self.daily_application_count = len(today_apps)
            self.applied_jobs_today = set(app.job_id for app in today_apps)
            
            logger.info(f"Loaded daily stats: {self.daily_application_count} applications today")
            
        except Exception as e:
            logger.error(f"Failed to load daily stats: {e}")
    
    async def get_application_status(self, days: int = 7) -> Dict[str, Any]:
        """Get application status summary."""
        try:
            applications = self.db_manager.get_applications_by_user(
                self.user_profile.user_id, 
                limit=100
            )
            
            # Filter recent applications
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            recent_apps = [
                app for app in applications 
                if app.application_date >= cutoff_date
            ]
            
            status_summary = {
                "total_applications": len(recent_apps),
                "status_breakdown": {},
                "needs_follow_up": [],
                "recent_responses": []
            }
            
            for app in recent_apps:
                status = app.status
                status_summary["status_breakdown"][status] = \
                    status_summary["status_breakdown"].get(status, 0) + 1
                
                if app.needs_follow_up():
                    status_summary["needs_follow_up"].append({
                        "job_title": app.job_title,
                        "company": app.company_name,
                        "days_since_applied": app.get_days_since_application()
                    })
            
            return status_summary
            
        except Exception as e:
            logger.error(f"Failed to get application status: {e}")
            return {"error": str(e)}
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        try:
            # Save user profile
            self.db_manager.save_user_profile(self.user_profile)
            logger.info("Agent cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
