"""Notion API integration for application tracking."""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from notion_client import Client
from notion_client.errors import APIResponseError

from ..config.settings import get_settings
from ..models.application import Application, ApplicationStatus
from ..models.job_posting import JobPosting

logger = logging.getLogger(__name__)


class NotionTracker:
    """Notion integration for tracking job applications."""
    
    def __init__(self, api_key: Optional[str] = None, database_id: Optional[str] = None):
        """Initialize Notion tracker."""
        self.settings = get_settings()
        self.api_key = api_key or self.settings.notion_api_key
        self.database_id = database_id or self.settings.notion_database_id
        
        if not self.api_key:
            raise ValueError("Notion API key is required")
        if not self.database_id:
            raise ValueError("Notion database ID is required")
        
        self.client = Client(auth=self.api_key)
        self._database_schema = None
    
    async def initialize_database(self) -> bool:
        """Initialize or verify the Notion database schema."""
        try:
            # Check if database exists and get its schema
            database = self.client.databases.retrieve(database_id=self.database_id)
            self._database_schema = database.get("properties", {})
            
            # Verify required properties exist
            required_properties = [
                "Job Title", "Company", "Status", "Application Date",
                "Job URL", "Method", "Days Since Applied", "Interviews",
                "Automated", "Needs Follow-up"
            ]
            
            missing_properties = []
            for prop in required_properties:
                if prop not in self._database_schema:
                    missing_properties.append(prop)
            
            if missing_properties:
                logger.warning(f"Missing properties in Notion database: {missing_properties}")
                # In a production system, you might want to create these properties
                # or provide instructions to the user
            
            logger.info("Notion database initialized successfully")
            return True
            
        except APIResponseError as e:
            logger.error(f"Failed to initialize Notion database: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error initializing Notion database: {e}")
            return False
    
    async def create_application_page(self, application: Application) -> Optional[str]:
        """Create a new page in Notion for the application."""
        try:
            # Prepare page properties
            properties = self._prepare_application_properties(application)
            
            # Create the page
            page = self.client.pages.create(
                parent={"database_id": self.database_id},
                properties=properties,
                children=self._create_page_content(application)
            )
            
            page_id = page["id"]
            page_url = page["url"]
            
            logger.info(f"Created Notion page for application {application.application_id}")
            return page_id
            
        except APIResponseError as e:
            logger.error(f"Failed to create Notion page: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating Notion page: {e}")
            return None
    
    async def update_application_page(self, page_id: str, application: Application) -> bool:
        """Update an existing Notion page with application data."""
        try:
            properties = self._prepare_application_properties(application)
            
            self.client.pages.update(
                page_id=page_id,
                properties=properties
            )
            
            logger.info(f"Updated Notion page {page_id} for application {application.application_id}")
            return True
            
        except APIResponseError as e:
            logger.error(f"Failed to update Notion page: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating Notion page: {e}")
            return False
    
    def _prepare_application_properties(self, application: Application) -> Dict[str, Any]:
        """Prepare Notion properties from application data."""
        properties = {
            "Job Title": {
                "title": [{"text": {"content": application.job_title}}]
            },
            "Company": {
                "rich_text": [{"text": {"content": application.company_name}}]
            },
            "Status": {
                "select": {"name": self._format_status(application.status)}
            },
            "Application Date": {
                "date": {"start": application.application_date.isoformat()}
            },
            "Job URL": {
                "url": str(application.job_url)
            },
            "Method": {
                "select": {"name": self._format_method(application.application_method)}
            },
            "Days Since Applied": {
                "number": application.get_days_since_application()
            },
            "Interviews": {
                "number": application.interviews_completed
            },
            "Automated": {
                "checkbox": application.automated_application
            },
            "Needs Follow-up": {
                "checkbox": application.needs_follow_up()
            }
        }
        
        # Add optional properties if they exist
        if application.salary_offered:
            properties["Salary Offered"] = {"number": application.salary_offered}
        
        if application.rejection_reason:
            properties["Rejection Reason"] = {
                "rich_text": [{"text": {"content": application.rejection_reason}}]
            }
        
        if application.current_interview_stage:
            properties["Interview Stage"] = {
                "select": {"name": application.current_interview_stage.title()}
            }
        
        return properties
    
    def _create_page_content(self, application: Application) -> List[Dict[str, Any]]:
        """Create page content blocks for the application."""
        content = []
        
        # Add application summary
        content.append({
            "object": "block",
            "type": "heading_2",
            "heading_2": {
                "rich_text": [{"text": {"content": "Application Summary"}}]
            }
        })
        
        summary_text = f"""
        Applied to {application.job_title} at {application.company_name} on {application.application_date.strftime('%Y-%m-%d')}.
        Application method: {application.application_method.value.replace('_', ' ').title()}.
        Current status: {application.status.value.replace('_', ' ').title()}.
        """
        
        content.append({
            "object": "block",
            "type": "paragraph",
            "paragraph": {
                "rich_text": [{"text": {"content": summary_text.strip()}}]
            }
        })
        
        # Add application questions and responses if any
        if application.application_questions:
            content.append({
                "object": "block",
                "type": "heading_3",
                "heading_3": {
                    "rich_text": [{"text": {"content": "Application Questions"}}]
                }
            })
            
            for question, answer in application.application_questions.items():
                content.append({
                    "object": "block",
                    "type": "paragraph",
                    "paragraph": {
                        "rich_text": [
                            {"text": {"content": f"Q: {question}\n", "annotations": {"bold": True}}},
                            {"text": {"content": f"A: {answer}"}}
                        ]
                    }
                })
        
        # Add communication history
        if application.responses:
            content.append({
                "object": "block",
                "type": "heading_3",
                "heading_3": {
                    "rich_text": [{"text": {"content": "Communication History"}}]
                }
            })
            
            for response in application.responses[-5:]:  # Last 5 responses
                content.append({
                    "object": "block",
                    "type": "paragraph",
                    "paragraph": {
                        "rich_text": [
                            {"text": {"content": f"{response.response_date.strftime('%Y-%m-%d')}: ", "annotations": {"bold": True}}},
                            {"text": {"content": response.content[:200] + ("..." if len(response.content) > 200 else "")}}
                        ]
                    }
                })
        
        # Add automation errors if any
        if application.automation_errors:
            content.append({
                "object": "block",
                "type": "heading_3",
                "heading_3": {
                    "rich_text": [{"text": {"content": "Automation Notes"}}]
                }
            })
            
            for error in application.automation_errors[-3:]:  # Last 3 errors
                content.append({
                    "object": "block",
                    "type": "paragraph",
                    "paragraph": {
                        "rich_text": [{"text": {"content": error}}]
                    }
                })
        
        return content
    
    def _format_status(self, status: ApplicationStatus) -> str:
        """Format status for Notion display."""
        return status.value.replace("_", " ").title()
    
    def _format_method(self, method) -> str:
        """Format application method for Notion display."""
        return method.value.replace("_", " ").title()
    
    async def get_application_statistics(self) -> Dict[str, Any]:
        """Get application statistics from Notion database."""
        try:
            # Query all pages in the database
            results = self.client.databases.query(database_id=self.database_id)
            
            stats = {
                "total_applications": 0,
                "status_breakdown": {},
                "method_breakdown": {},
                "recent_applications": 0,
                "interview_rate": 0,
                "response_rate": 0
            }
            
            total_apps = len(results["results"])
            stats["total_applications"] = total_apps
            
            if total_apps == 0:
                return stats
            
            # Analyze the results
            interviews = 0
            responses = 0
            recent_count = 0
            cutoff_date = datetime.utcnow().replace(day=1)  # This month
            
            for page in results["results"]:
                properties = page["properties"]
                
                # Status breakdown
                status = properties.get("Status", {}).get("select", {}).get("name", "Unknown")
                stats["status_breakdown"][status] = stats["status_breakdown"].get(status, 0) + 1
                
                # Method breakdown
                method = properties.get("Method", {}).get("select", {}).get("name", "Unknown")
                stats["method_breakdown"][method] = stats["method_breakdown"].get(method, 0) + 1
                
                # Count interviews
                interview_count = properties.get("Interviews", {}).get("number", 0)
                if interview_count > 0:
                    interviews += 1
                
                # Count responses (non-pending applications)
                if status not in ["Pending", "Submitted"]:
                    responses += 1
                
                # Count recent applications
                app_date_str = properties.get("Application Date", {}).get("date", {}).get("start")
                if app_date_str:
                    app_date = datetime.fromisoformat(app_date_str.replace("Z", "+00:00"))
                    if app_date >= cutoff_date:
                        recent_count += 1
            
            stats["recent_applications"] = recent_count
            stats["interview_rate"] = round((interviews / total_apps) * 100, 1) if total_apps > 0 else 0
            stats["response_rate"] = round((responses / total_apps) * 100, 1) if total_apps > 0 else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get application statistics: {e}")
            return {"error": str(e)}
    
    async def search_applications(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search applications in Notion database with filters."""
        try:
            # Build Notion query filters
            notion_filters = []
            
            if "status" in filters:
                notion_filters.append({
                    "property": "Status",
                    "select": {"equals": filters["status"]}
                })
            
            if "company" in filters:
                notion_filters.append({
                    "property": "Company",
                    "rich_text": {"contains": filters["company"]}
                })
            
            if "needs_followup" in filters:
                notion_filters.append({
                    "property": "Needs Follow-up",
                    "checkbox": {"equals": filters["needs_followup"]}
                })
            
            query_filter = {}
            if notion_filters:
                if len(notion_filters) == 1:
                    query_filter = notion_filters[0]
                else:
                    query_filter = {"and": notion_filters}
            
            # Execute query
            results = self.client.databases.query(
                database_id=self.database_id,
                filter=query_filter if query_filter else None
            )
            
            return results["results"]
            
        except Exception as e:
            logger.error(f"Failed to search applications: {e}")
            return []
