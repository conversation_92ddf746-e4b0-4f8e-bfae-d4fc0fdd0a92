# AI Provider API Keys (choose one or more)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here
AZURE_OPENAI_KEY=your_azure_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Notion Integration
NOTION_API_KEY=your_notion_integration_token_here
NOTION_DATABASE_ID=your_notion_database_id_here

# Database Configuration
DATABASE_URL=sqlite:///job_applications.db
DATABASE_ENCRYPTION_KEY=your_32_character_encryption_key_here

# Security Settings
MASTER_PASSWORD=your_master_password_for_encryption
SECRET_KEY=your_secret_key_for_sessions

# Browser Settings
HEADLESS_MODE=true
BROWSER_TIMEOUT=30000
SCREENSHOT_ON_ERROR=true

# Job Search Settings
MAX_APPLICATIONS_PER_DAY=10
SEARCH_DELAY_SECONDS=5
APPLICATION_DELAY_SECONDS=10

# LinkedIn Credentials (encrypted and stored securely)
# These will be prompted during setup, not stored in .env
# LINKEDIN_EMAIL=<EMAIL>
# LINKEDIN_PASSWORD=your_password

# Other Job Portal Credentials
# INDEED_EMAIL=<EMAIL>
# GLASSDOOR_EMAIL=<EMAIL>

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/job_applier.log

# Notification Settings (optional)
SLACK_WEBHOOK_URL=your_slack_webhook_url
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Development Settings
DEBUG=false
DEVELOPMENT_MODE=false
