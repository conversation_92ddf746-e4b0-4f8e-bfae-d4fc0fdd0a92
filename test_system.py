#!/usr/bin/env python3
"""
Test script to verify Job Auto Applier system is working correctly.
"""

import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        from job_auto_applier.config.settings import get_settings
        from job_auto_applier.config.encryption import EncryptionManager
        from job_auto_applier.models.user_profile import UserProfile
        from job_auto_applier.models.database import get_database_manager
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("🔧 Testing configuration...")
    
    try:
        from job_auto_applier.config.settings import get_settings
        settings = get_settings()
        
        # Check AI provider
        provider = settings.get_ai_provider()
        print(f"✅ AI Provider: {provider}")
        
        # Check basic settings
        print(f"✅ Max applications per day: {settings.max_applications_per_day}")
        print(f"✅ Browser timeout: {settings.browser_timeout}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_database():
    """Test database connectivity."""
    print("💾 Testing database...")
    
    try:
        from job_auto_applier.models.database import get_database_manager
        db_manager = get_database_manager()
        
        # Try to create tables
        db_manager.create_tables()
        print("✅ Database tables created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_encryption():
    """Test encryption functionality."""
    print("🔐 Testing encryption...")
    
    try:
        from job_auto_applier.config.encryption import EncryptionManager
        
        # Test with a known password
        manager = EncryptionManager("test_password")
        
        # Test encryption/decryption
        test_data = "This is a test message"
        encrypted = manager.encrypt(test_data)
        decrypted = manager.decrypt(encrypted)
        
        if decrypted == test_data:
            print("✅ Encryption/decryption working correctly")
            return True
        else:
            print("❌ Encryption test failed: data mismatch")
            return False
            
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        return False

def test_profile_loading():
    """Test loading Hemanth's profile."""
    print("👤 Testing profile loading...")
    
    try:
        from job_auto_applier.models.database import get_database_manager
        
        db_manager = get_database_manager()
        profile = db_manager.get_user_profile("hemanth_kiran_polu")
        
        if profile:
            print(f"✅ Profile loaded: {profile.personal_info.full_name}")
            print(f"✅ Target roles: {', '.join(profile.job_preferences.target_roles[:3])}")
            print(f"✅ Email: {profile.personal_info.email}")
            return True
        else:
            print("⚠️ Profile not found - run setup_hemanth_profile.py first")
            return False
            
    except Exception as e:
        print(f"❌ Profile loading test failed: {e}")
        return False

def test_ai_connectivity():
    """Test AI provider connectivity."""
    print("🤖 Testing AI connectivity...")
    
    try:
        from job_auto_applier.config.settings import get_settings
        settings = get_settings()
        
        if settings.openai_api_key and settings.openai_api_key.startswith("sk-"):
            print("✅ OpenAI API key configured")
        
        if settings.anthropic_api_key and settings.anthropic_api_key.startswith("sk-ant-"):
            print("✅ Anthropic API key configured")
        
        if settings.google_api_key:
            print("✅ Google API key configured")
        
        return True
        
    except Exception as e:
        print(f"❌ AI connectivity test failed: {e}")
        return False

def test_browser_automation():
    """Test browser automation setup."""
    print("🌐 Testing browser automation...")
    
    try:
        import playwright
        from playwright.sync_api import sync_playwright
        
        # Test that playwright is installed
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto("https://example.com")
            title = page.title()
            browser.close()
            
        if title:
            print("✅ Browser automation working")
            return True
        else:
            print("❌ Browser automation test failed")
            return False
            
    except Exception as e:
        print(f"❌ Browser automation test failed: {e}")
        print("💡 Try running: playwright install chromium --with-deps")
        return False

def main():
    """Run all tests."""
    print("🚀 Job Auto Applier System Test")
    print("=" * 40)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Database", test_database),
        ("Encryption", test_encryption),
        ("Profile Loading", test_profile_loading),
        ("AI Connectivity", test_ai_connectivity),
        ("Browser Automation", test_browser_automation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 20)
        if test_func():
            passed += 1
        else:
            print(f"💡 Fix the {test_name.lower()} issue and try again")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your Job Auto Applier is ready to use.")
        print("\n📋 Next Steps:")
        print("1. Run a dry run: python -m job_auto_applier.cli.main run --dry-run")
        print("2. Start applying: python -m job_auto_applier.cli.main run")
        return 0
    else:
        print("⚠️ Some tests failed. Please fix the issues before proceeding.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
