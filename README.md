# Job Auto Applier 🤖

An intelligent, persistent job application agent that leverages the browser-use library for fully automated job searching, application submission, and tracking for software engineering and cloud engineering positions.

## Features ✨

- **🔄 Fully Automated**: Daily job searches and applications with zero manual intervention
- **🔒 Secure Setup**: One-time secure collection and storage of all user information
- **🌐 Multi-Platform**: Supports LinkedIn, Indeed, Glassdoor, and company career pages
- **🧠 AI-Powered**: Advanced AI logic for handling novel application questions
- **📊 Smart Tracking**: Comprehensive application tracking via Notion API
- **🛡️ Privacy-First**: End-to-end encryption for all personal data
- **⚡ Browser Automation**: Powered by the cutting-edge browser-use library

## Quick Start 🚀

### Prerequisites

- Python 3.11 or higher
- Chrome/Chromium browser
- Notion account (for tracking)
- OpenAI or Anthropic API key

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/HemanthKiranPolu/JOB_Auto_Appllier.git
   cd JOB_Auto_Appllier
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   playwright install chromium --with-deps
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and preferences
   ```

4. **Initialize the system**
   ```bash
   python -m job_auto_applier setup
   ```

5. **Start the agent**
   ```bash
   python -m job_auto_applier run
   ```

## Configuration 🔧

### One-Time Setup

During the initial setup, the agent will securely collect:

- **Personal Information**: Name, email, phone, location
- **Resume/CV**: Upload and store your resume
- **Job Preferences**: Roles, locations, salary range, company types
- **Application Answers**: Standard responses to common questions
- **Credentials**: LinkedIn and other job portal login information
- **Notion Integration**: API key and database setup

### Job Search Criteria

Configure your job search preferences:

```python
# Example configuration
job_preferences = {
    "roles": ["Software Engineer", "Cloud Engineer", "DevOps Engineer"],
    "experience_levels": ["Entry Level", "Internship", "Associate"],
    "locations": ["Remote", "San Francisco", "New York"],
    "salary_min": 80000,
    "company_sizes": ["Startup", "Mid-size", "Enterprise"],
    "keywords": ["Python", "AWS", "Kubernetes", "React"]
}
```

## How It Works 🔄

### Daily Workflow

1. **🔍 Job Discovery**: Searches LinkedIn and other portals for new postings
2. **📋 Filtering**: Applies your criteria to identify relevant opportunities
3. **🤖 Application**: Automates form filling, resume upload, and submission
4. **💬 AI Responses**: Handles novel questions using advanced AI logic
5. **📊 Tracking**: Logs all activity to your Notion database
6. **🔄 Monitoring**: Tracks application status and follow-ups

### Supported Platforms

- **LinkedIn**: Easy Apply and external redirects
- **Indeed**: Direct applications and company redirects
- **Glassdoor**: Job applications and company pages
- **Company Websites**: Direct career page applications
- **AngelList**: Startup job applications

## Architecture 🏗️

```
job_auto_applier/
├── config/          # Configuration and user profile management
├── models/          # Data models and database schemas
├── automation/      # Browser automation using browser-use
├── ai/             # AI logic for question answering
├── tracking/       # Notion API integration and tracking
├── scheduler/      # Daily automation and orchestration
├── security/       # Encryption and secure storage
└── cli/            # Command-line interface
```

## Security & Privacy 🔒

- **End-to-End Encryption**: All personal data encrypted at rest
- **Secure Credential Storage**: Uses system keyring for sensitive data
- **No Data Sharing**: Your information never leaves your system
- **Privacy Compliance**: GDPR and CCPA compliant design
- **Audit Logging**: Complete audit trail of all actions

## Tracking & Analytics 📊

The system maintains detailed records in Notion:

- **Job Details**: Company, role, salary, location, description
- **Application Status**: Applied, rejected, interview, offer
- **Source Tracking**: Which platform the job was found on
- **Timeline**: Application date, response times, follow-ups
- **Success Metrics**: Application-to-interview ratios, response rates

## Advanced Features 🚀

### AI-Powered Responses

The system uses advanced AI to handle:
- Novel application questions
- Cover letter customization
- Resume tailoring for specific roles
- Interview scheduling responses

### Error Handling

Robust error handling for:
- CAPTCHA detection and alerts
- Network timeouts and retries
- Form validation errors
- Rate limiting and delays

### Customization

Highly customizable:
- Custom application templates
- Role-specific response strategies
- Company-specific preferences
- Industry-specific keywords

## Usage Examples 💡

### Basic Daily Run
```bash
# Run daily job search and applications
job-applier run --max-applications 10
```

### Setup New Profile
```bash
# Interactive setup for new user
job-applier setup --profile software-engineer
```

### Check Status
```bash
# View recent applications and status
job-applier status --days 7
```

### Update Preferences
```bash
# Modify job search criteria
job-applier config --update-preferences
```

## Contributing 🤝

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License 📄

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support 💬

- **Issues**: [GitHub Issues](https://github.com/HemanthKiranPolu/JOB_Auto_Appllier/issues)
- **Discussions**: [GitHub Discussions](https://github.com/HemanthKiranPolu/JOB_Auto_Appllier/discussions)
- **Email**: <EMAIL>

## Disclaimer ⚠️

This tool is for educational and personal use only. Please ensure compliance with the terms of service of job platforms and applicable laws in your jurisdiction. Use responsibly and ethically.

---

**Made with ❤️ for job seekers everywhere**
